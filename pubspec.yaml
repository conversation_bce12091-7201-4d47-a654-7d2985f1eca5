name: skillapp
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and  patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+2

environment:
  sdk: "^3.0.0"
  #sdk: '>=2.19.6 <3.10.5'
  #sdk: '>=3.0.0 <3.7.10'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  carousel_slider: ^5.0.0
  cloud_firestore: ^5.1.0
  collection: ^1.17.1
  cupertino_icons: ^1.0.2
  dartz: ^0.10.1
  device_preview: ^1.1.0
  dots_indicator: ^3.0.0
  equatable: ^2.0.5
  firebase_analytics: ^11.2.0
  #firebase_analytics: ^10.1.6
  firebase_auth: ^5.1.2
  firebase_core: ^3.2.0
  # firebase_core: ^2.11.0
  firebase_performance: ^0.10.0+3
  firebase_storage: ^12.1.1
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  flutter_stripe: ^9.5.0+1
  url_launcher: ^6.2.1
  http: ^1.1.0
  fl_chart: ^0.71.0
  flutter_launcher_icons: ^0.13.1
  flutter_showcaseview: ^2.1.3
  flutter_speed_dial: ^7.0.0
  flutter_spinkit: ^5.1.0
  flutter_svg: ^2.0.7
  get_it: ^7.6.4
  go_router: ^14.2.1
  google_fonts: ^6.2.1
  google_sign_in: ^6.1.0
  hydrated_bloc: ^9.1.2
  intl: any
  json_annotation: ^4.8.1
  provider: ^6.0.5
  rename_app: ^1.3.1
  shared_preferences: ^2.2.2
  showcaseview: ^3.0.0
  super_tooltip: ^2.0.4
  synchronized: ^3.1.0
  fluttertoast: ^8.2.12
  form_validator: ^2.1.1
  table_calendar: ^3.0.9
  percent_indicator: ^4.2.3
  change_app_package_name: ^1.1.0
  file_picker: ^8.1.4
  excel: ^4.0.6
  uuid: ^3.0.6
  pdf: ^3.10.7
  printing: ^5.11.1
  path_provider: ^2.1.1
  excel_to_json: ^1.4.0
  cached_network_image: ^3.3.1

dev_dependencies:
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  json_serializable: ^6.7.0
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  #image_path: "assets/images/logo/fullBabyNinja.png"
  #image_path: "assets/images/logo/sideninja.png"
  #image_path: "assets/images/logo/ninja-icon-face.png"
  image_path: "assets/images/logo/selectiveNinja.png"

  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/logo/selectiveNinja.png"
    background_color: "#fffff"
    theme_color: "#fffff"
  windows:
    generate: true
    image_path: "assets/images/logo/sideninja.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/logo/sideninja.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/google.png
    - assets/images/FirstMedal.png
    - assets/images/graduationHat.png
    - assets/images/plane.png
    - assets/images/boy-exploding-party-popper.png
    - assets/images/checkcircle.png
    - assets/images/dashboard/ellipse-11-bg.png
    - assets/images/dashboard/ios-books-application.png
    - assets/images/dashboard/pencil.png
    - assets/images/dashboard/group-22.png
    - assets/images/dashboard/awardstarfill0wght400grad0opsz24-1.png
    - assets/images/dashboard/auto-group-j3x1.png
    - assets/images/dashboard/group-9.png
    - assets/images/dashboard/ellipse-11-bg.png
    - assets/images/dashboard/auto-group-sgxw.png
    - assets/images/dashboard/menufill0wght400grad0opsz24-1.png
    - assets/images/dashboard/calculator.png
    - assets/images/dashboard/group-42.png
    - assets/images/dashboard/light-bulb.png
    - assets/images/backbtn_appbar.svg
    - assets/images/practice/next.svg
    - assets/images/practice/flag.svg
    - assets/images/practice/hint.svg
    - assets/images/practice/pizza_0.png
    - assets/images/practice/pizza_1.png
    - assets/images/practice/Grid_question/Option_1.png
    - assets/images/practice/Grid_question/Option_2.png
    - assets/images/practice/Grid_question/Option_3.png
    - assets/images/practice/Grid_question/Option_4.png
    - assets/images/Tick.png
    - assets/images/Retry.svg
    - assets/images/profileHeader.png
    - assets/images/profilePic.png
    - assets/images/moderate.svg
    - assets/images/hard.svg
    - assets/images/veryhard.svg
    - assets/images/other.svg
    - assets/images/logo/fullBabyNinja.png
    - assets/images/logo/sideninja.png
    - assets/images/logo/ninja-icon-face.png
    - assets/images/practice/hardFlag.svg
    - assets/images/award_star.svg
    - assets/images/savedQuestions/easy.png
    - assets/images/savedQuestions/moderate.png
    - assets/images/savedQuestions/hard.png
    - assets/images/savedQuestions/veryhard.png
    - assets/images/savedQuestions/selected.png
    - assets/images/savedQuestions/allFlag.png
    - assets/images/practice/flagSelected.svg
    - assets/images/dashboard/Ellipse_63.png
    - assets/images/dashboard/Flask.png
    - assets/images/dashboard/Ellipse_66.png
    - assets/images/dashboard/Group_91.png
    - assets/images/edit_profileimage.svg
    - assets/images/edit_profileimage.png
    - assets/images/Profile/avatars/avatar_1.svg
    - assets/images/Profile/avatars/avatar_2.svg
    - assets/images/Profile/avatars/avatar_3.svg
    - assets/images/Profile/avatars/avatar_4.svg
    - assets/images/Profile/avatars/avatar_5.svg
    - assets/images/Profile/avatars/avatar_6.svg
    - assets/images/changePassword/star_shape_1.png
    - assets/images/changePassword/lockAndKey.png
    - assets/images/streaks/streak_1.png
    - assets/images/streaks/done_symbol.png
    - assets/images/practice/progress_completion.png
    - assets/images/dashboard/blueNinja.png
    - assets/images/dashboard/xp_logo.png
    - assets/images/dashboard/ninjacoin_logo.png
    - assets/images/dashboard/completed_tick.png
    - assets/images/dashboard/batman_face.png
    - assets/images/logo/selectiveNinja.png
    - assets/images/userLevelMap/userlevel1.png
    - assets/images/userLevelMap/userlevelflag1.png
    - assets/images/userLevelMap/userlevellocked1.png
    - assets/images/userLevelMap/userlevelpath1.png
    - assets/images/logo/logo.svg
    - assets/images/web/web_blueNinja.svg
    - assets/images/web/web_blueNinja.png
    - assets/images/web/questions/save_question.svg
    - assets/images/practice/web/arrow_forward.svg
    - assets/images/practice/web/list.svg
    - assets/images/practice/web/daily_test_img.svg
    - assets/images/practice/web/daily_test_img.png
    - assets/images/practice/web/save.svg
    - assets/images/dashboard/sectional/sectional_test_reading.png
    - assets/images/dashboard/sectional/sectional_test_protractor_maths.png
    - assets/images/dashboard/sectional/sectional_test_thinkingskills.png
    - assets/images/dashboard/sectional/sectional_test_writing.png
    - assets/images/dashboard/sectional/sectional_test_hand_maths.png
    - assets/images/dashboard/test_Assessment_img.png

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
