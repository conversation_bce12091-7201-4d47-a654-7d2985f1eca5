import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/breadcrumb_widget.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/enhanced_navigation_rail.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/web_header.dart';

class WebMainContentAreaWidget extends StatefulWidget {
  final Widget child;

  const WebMainContentAreaWidget({super.key, required this.child});

  @override
  State<WebMainContentAreaWidget> createState() =>
      _WebMainContentAreaWidgetState();
}

class _WebMainContentAreaWidgetState extends State<WebMainContentAreaWidget> {
  int _selectedIndex = 0;
  String headerTitle = '';
  List<BreadcrumbItem>? breadcrumbItems;
  void _onDestinationSelected(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        headerTitle = 'Home';
        // Set breadcrumb for Home with just one item
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home', isActive: true),
        ];
        context.go('/home');
        break;
      case 1:
        headerTitle = 'Learning';
        // Set breadcrumb for Learning > Maths
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home'),
          BreadcrumbItem(
              label: 'Maths', route: '/practice/maths', isActive: true),
        ];
        context.go('/practice/maths'); // Default to maths practice
        break;
      case 2:
        headerTitle = 'Leaderboard';
        // Set breadcrumb for Leaderboard
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home'),
          BreadcrumbItem(
              label: 'Leaderboard', route: '/leaderboard', isActive: true),
        ];
        context.go('/leaderboard');
        break;
      case 3:
        headerTitle = 'Past Tests';
        // Set breadcrumb for Past Tests
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home'),
          BreadcrumbItem(
              label: 'Past Tests', route: '/past-tests', isActive: true),
        ];
        context.go('/past-tests');
        break;
      case 4:
        headerTitle = 'Subscription';
        // Set breadcrumb for Subscription
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home'),
          BreadcrumbItem(
              label: 'Subscription', route: '/subscription', isActive: true),
        ];
        context.go('/subscription');
        break;
      case 5:
        headerTitle = 'Profile & Settings';
        // Set breadcrumb for Profile
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home'),
          BreadcrumbItem(
              label: 'Profile & Settings', route: '/profile', isActive: true),
        ];
        context.go('/profile');
        break;
      case 6:
        headerTitle = 'Quiz Demo';
        // Set breadcrumb for Quiz Demo
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home'),
          BreadcrumbItem(
              label: 'Quiz Demo', route: '/quizDemo', isActive: true),
        ];
        context.go('/quizDemo');
        break;
      default:
        headerTitle = 'Home';
        // Set breadcrumb for Home with just one item
        breadcrumbItems = [
          BreadcrumbItem(label: 'Home', route: '/home', isActive: true),
        ];
        context.go('/home');
    }
  }

  void _updateHeaderForCurrentRoute(BuildContext context) {
    final currentRoute = GoRouterState.of(context).uri.toString();

    if (currentRoute.startsWith('/test-result/') &&
        !currentRoute.contains('/question/')) {
      headerTitle = 'Test Results';
      breadcrumbItems = [
        BreadcrumbItem(label: 'Home', route: '/home'),
        BreadcrumbItem(label: 'Past Tests', route: '/past-tests'),
        BreadcrumbItem(
            label: 'Test Results', route: currentRoute, isActive: true),
      ];
    }
    // Note: Question details are now handled as standalone screens outside the shell route
  }

  @override
  Widget build(BuildContext context) {
    // Update header title and breadcrumbs based on current route
    _updateHeaderForCurrentRoute(context);

    return Scaffold(
      body: Row(
        // mainAxisAlignment: MainAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            constraints: const BoxConstraints(maxWidth: 300),
            child: Column(
              children: [
                //  const WebLogoDisplay(),
                Expanded(
                  child: EnhancedNavigationRail(
                    selectedIndex: _selectedIndex,
                    onDestinationSelected: _onDestinationSelected,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: kWebMainContentBgColor,
                // color: kWebMainContentBgGreyColor,
                boxShadow: [
                  BoxShadow(
                    color: Color(0x1E000000),
                    blurRadius: 24,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  ),
                ],
              ),
              // Container(
              // width: double.infinity,
              //  constraints: const BoxConstraints(maxWidth: 1200),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  WebHeaderWidget(
                    headerTitle: headerTitle,
                    breadcrumbItems: breadcrumbItems,
                  ),
                  Expanded(
                    // child: SingleChildScrollView(
                    // child: Center(
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 1600),
                      // This padding parameter need to be taken care by individual rendering widget
                      //  padding: const EdgeInsets.all(40),
                      child: widget.child,
                    ),
                    // ),
                    // ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
