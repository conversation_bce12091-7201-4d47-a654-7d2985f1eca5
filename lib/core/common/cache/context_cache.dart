import 'dart:math';

import 'package:flutter/services.dart';
import 'package:skillapp/core/common/entities/flagged_question.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/entities/subjects.dart';
import 'package:skillapp/core/common/features/user/domain/entities/user.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/utils/repository_constants.dart';

abstract class ModuleCache {
  void set(String key, dynamic value);

  dynamic get(String key);

  void remove(String key);

  void clear();
}

abstract class CacheContext {
  void set(String key, dynamic value);

  dynamic get(String key);

  void remove(String key);

  void clear();

  dynamic getSubject(String subjectId);

  List<Subject> getSubjectList();

  bool sujectCacheExists();

  void addToSubjectList(Subject subject);

  void removeSubjectFromList(String subjectId);

  dynamic getQuestionsForSubject(String subjectId);

  dynamic getSingleRandomQuestionForGivenSubject(String subjectId);

  bool isSubjectPresentInQuestionBank(String subjectId);

  void addQuestionListToQuestionBank(
      String subjectId, Map<String, QuestionAndAnswers> questions);

  void addQuestionToQuestionBank(String subjectId, QuestionAndAnswers question);

  void removeQuestionFromQuestionBank(String subjectId, String questionId);

  void clearAllQuestionsForSubject(String subjectId);

  void populateSubjectCache(List<Subject> result);

  bool questionBankHasMinimumBuffer(String subjectId);

  LocalUser getCurrentUser();

  void setCurrentUser(LocalUser user);

  String get profileId;

  String get email;

  int get level;

  String get subscriptionType;

  void cleanCurrentUser();

  bool isCurrentUserDataAvailable();

  void clearFlaggedQuestions(String subjectId);

  List<FlaggedQuestion> getFlaggedQuestions(String subjectId);

  void addFlaggedQuestion(String subjectId, FlaggedQuestion question);

  void removeFlaggedQuestion(String subjectId, String questionId);

  void addFlaggedQuestionList(
      String subjectId, List<FlaggedQuestion> questionList);

  FlaggedQuestion getNextFlaggedQuestion(String subjectId, String questionId);

  bool isFlaggedQuestionPresent(String subjectId);

  ModuleCache getModuleCache(String moduleId);

  setModuleCache(String moduleId, ModuleCache moduleCache);
}

class CacheContextImpl extends CacheContext {
  //static final CacheContextImpl _instance = CacheContextImpl._internal();

  final List<Subject> _subjects = [];

  final Map<String, Map<String, QuestionAndAnswers>> _practiceQuestionBank = {};

  Map<String, List<FlaggedQuestion>> flaggedQuestionsMap = {};

  LocalUser _localUser = LocalUser.empty;

  // factory CacheContextImpl() {
  //   return _instance;
  // }

  //CacheContextImpl._internal();

  final Map<String, dynamic> _cache = {};

  @override
  void set(String key, dynamic value) {
    _cache[key] = value;
  }

  @override
  dynamic get(String key) {
    return _cache[key];
  }

  @override
  void remove(String key) {
    _cache.remove(key);
  }

  @override
  void clear() {
    _cache.clear();
  }

  @override
  void addToSubjectList(Subject subject) {
    //subjects ??= List.empty();
    //add the subject to the list
    _subjects.add(subject);
  }

  @override
  getSubject(String subjectId) {
    _subjects.firstWhere((element) => element.id == subjectId,
        orElse: () => Subject.empty());
  }

  @override
  getSubjectList() {
    //copy subject list and send a new list
    print("AW:Getting subjects list: $_subjects");
    return List.from(_subjects);
  }

  @override
  void removeSubjectFromList(String subjectId) {
    _subjects.removeWhere((element) => element.id == subjectId);
  }

  @override
  void clearAllQuestionsForSubject(String subjectId) {
    _practiceQuestionBank.remove(subjectId);
  }

  @override
  getQuestionsForSubject(String subjectId) {
    return _practiceQuestionBank.containsKey(subjectId)
        ? Map<String, QuestionAndAnswers>.from(
            _practiceQuestionBank[subjectId]!)
        : {};
  }

  @override
  void removeQuestionFromQuestionBank(String subjectId, String questionId) {
    if (_practiceQuestionBank.containsKey(subjectId)) {
      _practiceQuestionBank[subjectId]!.remove(questionId);
    }
  }

  @override
  void setQuestionsForSubject(String subjectId, QuestionAndAnswers question) {
    _practiceQuestionBank.containsKey(subjectId)
        ? _practiceQuestionBank[subjectId]!.addAll({question.id: question})
        : _practiceQuestionBank.addAll({
            subjectId: {question.id: question}
          });
  }

  @override
  bool sujectCacheExists() {
    return _subjects.isNotEmpty;
  }

  @override
  void populateSubjectCache(List<Subject> result) {
    _subjects.clear();
    _subjects.addAll(result);
  }

  @override
  bool questionBankHasMinimumBuffer(String subjectId) {
    return _practiceQuestionBank.containsKey(subjectId) &&
        _practiceQuestionBank[subjectId]!.length >
            RepositoryConstants.minimumBufferLength;
  }

  @override
  void cleanCurrentUser() {
    _localUser = LocalUser.empty;
  }

  @override
  LocalUser getCurrentUser() {
    return _localUser;
  }

  @override
  void setCurrentUser(LocalUser user) {
    _localUser = user;
  }

  @override
  bool isCurrentUserDataAvailable() {
    return _localUser.isNotEmpty;
  }

  @override
  String get email => _localUser.email;

  @override
  String get profileId => _localUser.currentProfile.id;

  @override
  int get level => _localUser.currentProfile.level;

  @override
  String get subscriptionType => _localUser.subscriptionType;

  @override
  bool isSubjectPresentInQuestionBank(String subjectId) {
    return _practiceQuestionBank.containsKey(subjectId);
  }

  @override
  void addQuestionListToQuestionBank(
      String subjectId, Map<String, QuestionAndAnswers> questions) {
    _practiceQuestionBank.containsKey(subjectId)
        ? _practiceQuestionBank[subjectId]!.addAll(questions)
        : _practiceQuestionBank.putIfAbsent(subjectId, () => questions);
  }

  // Ranjith - This function is not used anywhere in the code
  @override
  void addQuestionToQuestionBank(
      String subjectId, QuestionAndAnswers question) {
    _practiceQuestionBank.containsKey(subjectId)
        ? _practiceQuestionBank[subjectId]!.addAll({question.id: question})
        : _practiceQuestionBank.putIfAbsent(
            subjectId, () => {question.id: question});
  }

  @override
  QuestionAndAnswers getSingleRandomQuestionForGivenSubject(String subjectId) {
    if (_practiceQuestionBank.containsKey(subjectId)) {
      Map<String, QuestionAndAnswers> questions =
          _practiceQuestionBank[subjectId]!;
      String randomKey = getRandomKey(questions);
      return questions[randomKey]!;
    } else {
      return QuestionAndAnswers.emptyQuestion();
    }
  }

  String getRandomKey(Map<String, dynamic> map) {
    List<String> keys = map.keys.toList();
    Random random = Random();
    int randomIndex = random.nextInt(keys.length);
    String randomKey = keys[randomIndex];
    return randomKey;
  }

  @override
  void addFlaggedQuestion(String subjectId, FlaggedQuestion question) {
    flaggedQuestionsMap.containsKey(subjectId)
        ? flaggedQuestionsMap[subjectId]!.add(question)
        : flaggedQuestionsMap.putIfAbsent(subjectId, () => [question]);
  }

  @override
  void addFlaggedQuestionList(
      String subjectId, List<FlaggedQuestion> questionList) {
    flaggedQuestionsMap.containsKey(subjectId)
        ? flaggedQuestionsMap[subjectId]!.addAll(questionList)
        : flaggedQuestionsMap.putIfAbsent(subjectId, () => questionList);
  }

  @override
  void clearFlaggedQuestions(String subjectId) {
    flaggedQuestionsMap.remove(subjectId);
  }

  @override
  List<FlaggedQuestion> getFlaggedQuestions(String subjectId) {
    return flaggedQuestionsMap.containsKey(subjectId)
        ? flaggedQuestionsMap[subjectId]!
        : [];
  }

  @override
  FlaggedQuestion getNextFlaggedQuestion(String subjectId, String questionId) {
    if (flaggedQuestionsMap.containsKey(subjectId)) {
      List<FlaggedQuestion> flaggedQuestions = flaggedQuestionsMap[subjectId]!;
      int index = flaggedQuestions
          .indexWhere((element) => element.questionId == questionId);
      if (index == flaggedQuestions.length - 1) {
        return const FlaggedQuestion.empty();
      } else {
        return flaggedQuestions[index + 1];
      }
    } else {
      return const FlaggedQuestion.empty();
    }
  }

  @override
  bool isFlaggedQuestionPresent(String subjectId) {
    return flaggedQuestionsMap.containsKey(subjectId) &&
        flaggedQuestionsMap[subjectId]!.isNotEmpty;
  }

  @override
  void removeFlaggedQuestion(String subjectId, String questionId) {
    if (flaggedQuestionsMap.containsKey(subjectId)) {
      flaggedQuestionsMap[subjectId]!
          .removeWhere((element) => element.questionId == questionId);
    }
  }

  final Map<String, dynamic> _moduleCache = {};

  @override
  ModuleCache getModuleCache(String moduleId) {
    return _moduleCache.containsKey(moduleId) && _moduleCache[moduleId] != null
        ? _moduleCache[moduleId]
        : throw ServerException(
            message: "No cache present for given module id $ModifierKey",
            statusCode: '400');
  }

  @override
  setModuleCache(String moduleId, ModuleCache moduleCache) {
    _moduleCache.putIfAbsent(moduleId, () => moduleCache);
  }
}
