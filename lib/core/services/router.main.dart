part of 'router.dart';

GoRouter skillAppRouter(BuildContext context) {
  print(
      "JB: Router Device Type  ${ScreenUtil.getDeviceType(context).toString()}");

  String screenRes = ScreenUtil.getDeviceType(context).toString();
  if (screenRes != 'Desktop') {
    // if (!kIsWeb) {
    return GoRouter(routes: [
      /* ScreenUtil.getDeviceType(context).toString() == 'Desktop'
        ? ShellRoute(
            builder: (context, state, child) =>
                WebMainContentAreaWidget(child: child),
            routes: [
              GoRoute(
                path: '/home',
                pageBuilder: (context, state) {
                  return NoTransitionPage(
                    child: MultiRepositoryProvider(
                      providers: [
                        BlocProvider(
                          create: (context) => sl<HomeBloc>(),
                        ),
                        BlocProvider(
                          create: (context) => sl<DashboardSummaryBloc>(),
                        ),
                      ],
                      child: HomeScreen.routeBuilder(context, state),
                    ),
                  );
                },
              ),
              GoRoute(
                path: '/practice/:name',
                pageBuilder: (context, state) {
                  final String paramValue = state.pathParameters['name'] ?? '';
                  return NoTransitionPage(
                    child: MultiBlocProvider(
                      providers: [
                        BlocProvider(create: (context) => sl<PracticeBloc>()),
                        BlocProvider.value(value: sl<TestsBloc>()),
                      ],
                      child: PracticeScreen.routeBuilder(
                          context, state, paramValue),
                    ),
                  );
                },
              ),
              GoRoute(
                path: '/profile',
                pageBuilder: (context, state) => NoTransitionPage(
                  child: BlocProvider.value(
                    value: sl<TestsBloc>(),
                    child: WebMainProfileWidget.routeBuilder(context, state),
                  ),
                ),
              ),
              GoRoute(
                path: '/editProfile',
                pageBuilder: (context, state) => NoTransitionPage(
                  child: MultiBlocProvider(
                    providers: [
                      BlocProvider.value(value: sl<TestsBloc>()),
                      BlocProvider(create: (context) => sl<EditProfileBloc>()),
                    ],
                    child: EditProfile.routeBuilder(context, state),
                  ),
                ),
              ),
              GoRoute(
                path: '/flaggedSubjects',
                pageBuilder: (context, state) => NoTransitionPage(
                  child: BlocProvider(
                    create: (context) => FlaggedQuestionsBloc(
                        fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                        fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
                    child: FlaggedSubjects.routeBuilder(context, state),
                  ),
                ),
              ),
              GoRoute(
                path: '/flaggedQuestionList/:name',
                pageBuilder: (context, state) {
                  final String paramValue = state.pathParameters['name'] ?? '';
                  return NoTransitionPage(
                    child: BlocProvider(
                      create: (context) => FlaggedQuestionsBloc(
                          fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                          fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
                      child: FlaggedQuestionList.routeBuilder(
                          context, state, paramValue),
                    ),
                  );
                },
              ),
              GoRoute(
                path: '/history',
                pageBuilder: (context, state) => NoTransitionPage(
                  child: BlocProvider(
                    create: (context) => sl<AttemptHistoryBloc>(),
                    child: HistoryQuestionList.routeBuilder(context, state),
                  ),
                  // );
                ),
              ),
            ],
          )
        :*/
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/',
        pageBuilder: (context, state) => NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider<LoginCubit>(
                create: (_) => sl<LoginCubit>(),
              ),
              BlocProvider<SignupCubit>(
                create: (_) => sl<SignupCubit>(),
              ),
            ],
            child: LandingScreen.routeBuilder(context, state),
          ),
        ),
        redirect: (context, state) {
          if (sl<FirebaseAuth>().currentUser != null) {
            print("JB:Inside router user not null- printing from router");
            final user = sl<FirebaseAuth>().currentUser!;

            //context.read<UserProvider>().populateUserProfile();
            return "/home";
          } else {
            print(
                "JB:Inside router user is null hence not logged in - from router");
            return "/";
          }
        },
      ),
      GoRoute(
        path: '/logout',
        pageBuilder: (context, state) => NoTransitionPage(
          child: LandingScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/successpopup',
        pageBuilder: (context, state) => NoTransitionPage(
          child: SuccessPopUpScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/signin',
        pageBuilder: (context, state) => NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider<LoginCubit>(
                create: (_) => sl<LoginCubit>(),
              ),
              BlocProvider<SignupCubit>(
                create: (_) => sl<SignupCubit>(),
              ),
            ],
            child: SignInScreen.routeBuilder(context, state),
          ),
          //child: SignInScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
          path: '/signup',
          pageBuilder: (context, state) {
            //UserRepository userRepository = UserFirestoreRepository();
            return NoTransitionPage(
              child: BlocProvider(
                create: (context) => sl<SignupCubit>(),
                child: SignUpScreen.routeBuilder(context, state),
              ),
            );
          }),
      GoRoute(
        path: '/home',
        // path: '/',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: MultiRepositoryProvider(
              providers: [
                BlocProvider(
                  create: (context) => sl<HomeBloc>(),
                ),
                BlocProvider(
                  create: (context) => sl<DashboardSummaryBloc>(),
                ),
              ],
              child: HomeScreen.routeBuilder(context, state),
            ),
            // child: UserLevelMapWidget.routeBuilder(context, state),
          );
        },
      ),
      GoRoute(
        path: '/testscreen',
        // path: '/',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: BackEndTestingSupportcreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/uploadquestions',
        // path: '/',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => sl<ExcelFileUploadBloc>(),
              child: UploadScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/flaggedSubjects',
        pageBuilder: (context, state) => NoTransitionPage(
          child: BlocProvider(
            create: (context) => FlaggedQuestionsBloc(
                fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
            child: FlaggedSubjects.routeBuilder(context, state),
          ),
        ),
      ),
      GoRoute(
        path: '/flaggedQuestionList/:name',
        pageBuilder: (context, state) {
          final String paramValue = state.pathParameters['name'] ?? '';
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => FlaggedQuestionsBloc(
                  fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                  fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
              child:
                  FlaggedQuestionList.routeBuilder(context, state, paramValue),
            ),
          );
        },
      ),
      GoRoute(
        path: '/singleQuestionFlow/:bundleId/:questionId/:subject',
        pageBuilder: (context, state) {
          final String paramValue1 = state.pathParameters['bundleId'] ?? '';
          final String paramValue2 = state.pathParameters['questionId'] ?? '';
          final String paramValue3 = state.pathParameters['subject'] ?? '';
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => sl<PracticeBloc>(),
              child: SingleQuestionFlow.routeBuilder(
                  context, state, paramValue1, paramValue2, paramValue3),
            ),
          );
        },
      ),
      GoRoute(
        path: '/practice/:name',
        pageBuilder: (context, state) {
          final String paramValue = state.pathParameters['name'] ?? '';
          return NoTransitionPage(
            child: MultiBlocProvider(
              providers: [
                BlocProvider(create: (context) => sl<PracticeBloc>()),
                BlocProvider.value(value: sl<TestsBloc>()),
              ],
              child: PracticeScreen.routeBuilder(context, state, paramValue),
            ),
          );
        },
      ),
      GoRoute(
        path: '/history',
        pageBuilder: (context, state) => NoTransitionPage(
          child: BlocProvider(
            create: (context) => sl<AttemptHistoryBloc>(),
            child: HistoryQuestionList.routeBuilder(context, state),
          ),
          // );
        ),
      ),
      GoRoute(
        path: '/notifications',
        pageBuilder: (context, state) => NoTransitionPage(
          child: NotificationScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/editProfile',
        pageBuilder: (context, state) => NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider.value(value: sl<TestsBloc>()),
              BlocProvider(create: (context) => sl<EditProfileBloc>()),
            ],
            child: EditProfile.routeBuilder(context, state),
          ),
        ),
      ),
      GoRoute(
        path: '/changePassword',
        pageBuilder: (context, state) => NoTransitionPage(
          child: ChangePassword.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/streakCalendar',
        pageBuilder: (context, state) => NoTransitionPage(
          child: StreakCalendarPage.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/userLevelMap',
        pageBuilder: (context, state) => NoTransitionPage(
          child: UserLevelMapWidget.routeBuilder(context, state),
        ),
      ),
      // Test routes
      GoRoute(
        path: '/test-instructions',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: TestInstructionsScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test-confirmation',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: TestConfirmationScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/daily-test-confirmation',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: DailyTestConfirmationScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test/:testId',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: test_views.TestScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test-result/:testAttemptId',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: TestResultScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/daily-test-success/:testAttemptId',
        pageBuilder: (context, state) {
          final String testAttemptId =
              state.pathParameters['testAttemptId'] ?? '';
          return NoTransitionPage(
            child: DailyTestSuccessScreen(
              testAttemptId: testAttemptId,
              testTitle: 'Daily Test',
            ),
          );
        },
      ),
      GoRoute(
        path: '/test-result/:testAttemptId/question/:questionIndex',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: QuestionDetailScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/googlesignupprofile/:email/:name',
        pageBuilder: (context, state) {
          final String paramValue1 = state.pathParameters['email'] ?? '';
          final String paramValue2 = state.pathParameters['name'] ?? '';
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => sl<SignupCubit>(),
              child: GoogleSignUpProfileScreen.routeBuilder(
                  context, state, paramValue1, paramValue2),
            ),
          );
        },
      ),
    ]);
  } else {
    // Shell route for web only
    return GoRouter(routes: [
      ShellRoute(
        builder: (context, state, child) {
          return MultiBlocProvider(
            providers: [
              BlocProvider.value(
                value: sl<AppBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<HomeBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<DashboardSummaryBloc>(),
              ),
            ],
            child: WebMainContentAreaWidget(
              child: child,
            ),
          );
        },
        /*builder: (context, state, child) =>
            WebMainContentAreaWidget(child: child),*/
        routes: [
          GoRoute(
            path: '/home',
            pageBuilder: (context, state) {
              print("/home router inside shell route");
              return NoTransitionPage(
                child: HomeScreen.routeBuilder(context, state),
                /*  child: MultiRepositoryProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => sl<HomeBloc>(),
                    ),
                    BlocProvider(
                      create: (context) => sl<DashboardSummaryBloc>(),
                    ),
                  ],
                  child: HomeScreen.routeBuilder(context, state),*/
                //),
              );
            },
          ),
          GoRoute(
            path: '/practice/:name',
            pageBuilder: (context, state) {
              final String paramValue = state.pathParameters['name'] ?? '';
              return NoTransitionPage(
                child: MultiBlocProvider(
                  providers: [
                    BlocProvider(create: (context) => sl<PracticeBloc>()),
                    BlocProvider.value(value: sl<TestsBloc>()),
                  ],
                  child:
                      PracticeScreen.routeBuilder(context, state, paramValue),
                ),
              );
            },
          ),
          GoRoute(
            path: '/past-tests',
            pageBuilder: (context, state) => NoTransitionPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(create: (context) => sl<PastTestsBloc>()),
                  BlocProvider.value(value: sl<TestsBloc>()),
                ],
                child: PastTestsScreen.routeBuilder(context, state),
              ),
            ),
          ),
          GoRoute(
            path: '/test-summary/:testId',
            pageBuilder: (context, state) {
              final String testId = state.pathParameters['testId'] ?? '';
              return NoTransitionPage(
                child: TestSummaryPage(testId: testId),
              );
            },
          ),
          GoRoute(
            path: '/quizDemo',
            pageBuilder: (context, state) => NoTransitionPage(
              child: QuizDemoScreen.routeBuilder(context, state),
            ),
          ),
          GoRoute(
            path: '/subscription',
            pageBuilder: (context, state) => NoTransitionPage(
              child: SubscriptionPlanPage.routeBuilder(context, state),
            ),
          ),
          GoRoute(
            path: '/profile',
            pageBuilder: (context, state) => NoTransitionPage(
              child: BlocProvider.value(
                value: sl<TestsBloc>(),
                child: WebMainProfileWidget.routeBuilder(context, state),
              ),
            ),
          ),
          GoRoute(
            path: '/editProfile',
            pageBuilder: (context, state) => NoTransitionPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider.value(value: sl<TestsBloc>()),
                  BlocProvider(create: (context) => sl<EditProfileBloc>()),
                ],
                child: EditProfile.routeBuilder(context, state),
              ),
            ),
          ),
          GoRoute(
            path: '/flaggedSubjects',
            pageBuilder: (context, state) => NoTransitionPage(
              child: BlocProvider(
                create: (context) => FlaggedQuestionsBloc(
                    fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                    fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
                child: FlaggedSubjects.routeBuilder(context, state),
              ),
            ),
          ),
          GoRoute(
            path: '/flaggedQuestionList/:name',
            pageBuilder: (context, state) {
              final String paramValue = state.pathParameters['name'] ?? '';
              return NoTransitionPage(
                child: BlocProvider(
                  create: (context) => FlaggedQuestionsBloc(
                      fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                      fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
                  child: FlaggedQuestionList.routeBuilder(
                      context, state, paramValue),
                ),
              );
            },
          ),
          GoRoute(
            path: '/history',
            pageBuilder: (context, state) => NoTransitionPage(
              child: BlocProvider(
                create: (context) => sl<AttemptHistoryBloc>(),
                child: HistoryQuestionList.routeBuilder(context, state),
              ),
              // );
            ),
          ),
          GoRoute(
            path: '/test-result/:testAttemptId',
            pageBuilder: (context, state) {
              return NoTransitionPage(
                // Use the singleton TestsBloc instance
                child: BlocProvider.value(
                  value: sl<TestsBloc>(),
                  child: TestResultScreen.routeBuilder(context, state),
                ),
              );
            },
          ),
          GoRoute(
            path: '/daily-test-success/:testAttemptId',
            pageBuilder: (context, state) {
              final String testAttemptId =
                  state.pathParameters['testAttemptId'] ?? '';
              return NoTransitionPage(
                child: DailyTestSuccessScreen(
                  testAttemptId: testAttemptId,
                  testTitle: 'Daily Test',
                ),
              );
            },
          ),
        ],
      ),
      GoRoute(
        path: '/',
        pageBuilder: (context, state) => NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider<LoginCubit>(
                create: (_) => sl<LoginCubit>(),
              ),
              BlocProvider<SignupCubit>(
                create: (_) => sl<SignupCubit>(),
              ),
            ],
            child: LandingScreen.routeBuilder(context, state),
          ),
        ),
        redirect: (context, state) {
          if (sl<FirebaseAuth>().currentUser != null) {
            print("JB:Inside router user not null- printing from router");
            final user = sl<FirebaseAuth>().currentUser!;

            //context.read<UserProvider>().populateUserProfile();
            return "/home";
          } else {
            print(
                "JB:Inside router user is null hence not logged in - from router");
            return "/";
          }
        },
      ),
      GoRoute(
        path: '/logout',
        pageBuilder: (context, state) => NoTransitionPage(
          child: LandingScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/successpopup',
        pageBuilder: (context, state) => NoTransitionPage(
          child: SuccessPopUpScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/signin',
        pageBuilder: (context, state) => NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider<LoginCubit>(
                create: (_) => sl<LoginCubit>(),
              ),
              BlocProvider<SignupCubit>(
                create: (_) => sl<SignupCubit>(),
              ),
            ],
            child: SignInScreen.routeBuilder(context, state),
          ),
          //child: SignInScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
          path: '/signup',
          pageBuilder: (context, state) {
            //UserRepository userRepository = UserFirestoreRepository();
            return NoTransitionPage(
              child: BlocProvider(
                create: (context) => sl<SignupCubit>(),
                child: SignUpScreen.routeBuilder(context, state),
              ),
            );
          }),
      GoRoute(
        path: '/home',
        pageBuilder: (context, state) {
          print("/home router printing from non shell route");
          return NoTransitionPage(
            child: MultiRepositoryProvider(
              providers: [
                BlocProvider(
                  create: (context) => sl<HomeBloc>(),
                ),
                BlocProvider(
                  create: (context) => sl<DashboardSummaryBloc>(),
                ),
              ],
              child: HomeScreen.routeBuilder(context, state),
            ),
            // child: UserLevelMapWidget.routeBuilder(context, state),
          );
        },
      ),
      GoRoute(
        path: '/testscreen',
        // path: '/',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: BackEndTestingSupportcreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test-instructions',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: TestInstructionsScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test-confirmation',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: TestConfirmationScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/daily-test-confirmation',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: DailyTestConfirmationScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test/:testId',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: test_views.TestScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/uploadquestions',
        // path: '/',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => sl<ExcelFileUploadBloc>(),
              child: UploadScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
      GoRoute(
        path: '/flaggedSubjects',
        pageBuilder: (context, state) => NoTransitionPage(
          child: BlocProvider(
            create: (context) => FlaggedQuestionsBloc(
                fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
            child: FlaggedSubjects.routeBuilder(context, state),
          ),
        ),
      ),
      GoRoute(
        path: '/flaggedQuestionList/:name',
        pageBuilder: (context, state) {
          final String paramValue = state.pathParameters['name'] ?? '';
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => FlaggedQuestionsBloc(
                  fetchFlaggedQuestions: sl<FetchFlaggedQuestions>(),
                  fetchFlaggedSubjects: sl<FetchFlaggedSubjects>()),
              child:
                  FlaggedQuestionList.routeBuilder(context, state, paramValue),
            ),
          );
        },
      ),
      GoRoute(
        path: '/singleQuestionFlow/:bundleId/:questionId/:subject',
        pageBuilder: (context, state) {
          final String paramValue1 = state.pathParameters['bundleId'] ?? '';
          final String paramValue2 = state.pathParameters['questionId'] ?? '';
          final String paramValue3 = state.pathParameters['subject'] ?? '';
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => sl<PracticeBloc>(),
              child: SingleQuestionFlow.routeBuilder(
                  context, state, paramValue1, paramValue2, paramValue3),
            ),
          );
        },
      ),
      GoRoute(
        path: '/practice/:name',
        pageBuilder: (context, state) {
          final String paramValue = state.pathParameters['name'] ?? '';
          return NoTransitionPage(
            child: MultiBlocProvider(
              providers: [
                BlocProvider(create: (context) => sl<PracticeBloc>()),
                BlocProvider.value(value: sl<TestsBloc>()),
              ],
              child: PracticeScreen.routeBuilder(context, state, paramValue),
            ),
          );
        },
      ),
      GoRoute(
        path: '/history',
        pageBuilder: (context, state) => NoTransitionPage(
          child: BlocProvider(
            create: (context) => sl<AttemptHistoryBloc>(),
            child: HistoryQuestionList.routeBuilder(context, state),
          ),
          // );
        ),
      ),
      GoRoute(
        path: '/notifications',
        pageBuilder: (context, state) => NoTransitionPage(
          child: NotificationScreen.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/editProfile',
        pageBuilder: (context, state) => NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider.value(value: sl<TestsBloc>()),
              BlocProvider(create: (context) => sl<EditProfileBloc>()),
            ],
            child: EditProfile.routeBuilder(context, state),
          ),
        ),
      ),
      GoRoute(
        path: '/changePassword',
        pageBuilder: (context, state) => NoTransitionPage(
          child: ChangePassword.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/streakCalendar',
        pageBuilder: (context, state) => NoTransitionPage(
          child: StreakCalendarPage.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/userLevelMap',
        pageBuilder: (context, state) => NoTransitionPage(
          child: UserLevelMapWidget.routeBuilder(context, state),
        ),
      ),
      GoRoute(
        path: '/googlesignupprofile/:email/:name',
        pageBuilder: (context, state) {
          final String paramValue1 = state.pathParameters['email'] ?? '';
          final String paramValue2 = state.pathParameters['name'] ?? '';
          return NoTransitionPage(
            child: BlocProvider(
              create: (context) => sl<SignupCubit>(),
              child: GoogleSignUpProfileScreen.routeBuilder(
                  context, state, paramValue1, paramValue2),
            ),
          );
        },
      ),
      GoRoute(
        path: '/test-result/:testAttemptId/question/:questionIndex',
        pageBuilder: (context, state) {
          return NoTransitionPage(
            // Use the singleton TestsBloc instance
            child: BlocProvider.value(
              value: sl<TestsBloc>(),
              child: QuestionDetailScreen.routeBuilder(context, state),
            ),
          );
        },
      ),
    ]);
  }
}
