import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/core/common/widgets/common_widgets_config.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/practice/presentation/blocs/practice_bloc.dart';
import 'package:skillapp/src/practice/presentation/widgets/practice_floating_widget.dart';
import 'package:skillapp/src/practice/presentation/widgets/practice_footer.dart';

class SingleQuestionFlow extends StatefulWidget {
  final String bundleId;
  final String questionId;
  final String subject;

  const SingleQuestionFlow({
    required this.bundleId,
    required this.questionId,
    required this.subject,
    super.key,
  });

  factory SingleQuestionFlow.routeBuilder(BuildContext context,
      GoRouterState state, String bundleId, String questionId, String subject) {
    return SingleQuestionFlow(
        bundleId: bundleId, questionId: questionId, subject: subject);
  }

  @override
  State<SingleQuestionFlow> createState() => _SingleQuestionFlowState();
}

class _SingleQuestionFlowState extends State<SingleQuestionFlow> {
  @override
  void initState() {
    super.initState();
    print("AW: Before initState in SingleQuestionFlow");
    // contect.read<PracticeBloc>().add(Flow(delegate: delegate))
    context.read<PracticeBloc>().add(
          FetchSingleQuestionEvent(
              widget.questionId, widget.bundleId, widget.subject),
        );
    print("AW: After initState in SingleQuestionFlow");
  }

  @override
  Widget build(BuildContext context) {
    double baseWidth = 375;
    double fem = MediaQuery.of(context).size.width / baseWidth;
    double ffem = fem * 0.97;
    String appBarTitle = widget.subject;
    //bool questionLoaded = false;
    return BlocListener<PracticeBloc, PracticeState>(
      listener: (context, state) {
        if (state.isError) {
          //TODO-Handle error here
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0XFFF5F5F5),
        appBar: AppBarTemplate(
          appBarTitle: "Saved Question : $appBarTitle",
          actionType: 'NA',
        ),
        body: const SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: QuestionAnswerWidget(),
        ),
        floatingActionButton:
            BlocBuilder<PracticeBloc, PracticeState>(builder: (context, state) {
          return Builder(builder: (context) {
            if (state.lastAttemptStatus == AttemptStatus.success ||
                state.lastAttemptStatus == AttemptStatus.failure ||
                state.lastAttemptStatus == AttemptStatus.lastAttemptDone) {
              return HintAndSolutionWidget(state: state);
            } else {
              return Container();
            }
          });
        }),
        persistentFooterButtons: const [
          PraticeScreenFooter(),
        ],
      ),
    );
  }
}
