import 'package:skillapp/src/subscription_config/data/datasources/subscription_config_remote_datasource.dart';
import 'package:skillapp/src/subscription_config/domain/entities/subscription_config.dart';

import 'package:skillapp/src/subscription_config/domain/repos/subscription_config_repo.dart';

class SubscriptionConfigRepositoryImpl implements SubscriptionConfigRepository {
  final SubscriptionConfigRemoteDataSource _remoteDataSource;

  SubscriptionConfigRepositoryImpl({
    required SubscriptionConfigRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  @override
  Future<SubscriptionConfig?> getSubscriptionConfig(
      String subscriptionType) async {
    final configMap =
        await _remoteDataSource.fetchSubscriptionConfig(subscriptionType);
    if (configMap == null) return null;
    return SubscriptionConfig.fromMap(configMap);
  }

  @override
  Future<String?> fetchSubscriptionTypeByEmail(String email) async {
    return _remoteDataSource.fetchSubscriptionTypeByEmail(email);
  }
}
