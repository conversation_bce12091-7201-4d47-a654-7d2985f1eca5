import 'package:cloud_firestore/cloud_firestore.dart';

abstract class AttemptedQuestionCountRemoteDatasource {
  Future<int> getAttemptedQuestionCountForToday(String profileId);
}

class AttemptedQuestionCountRemoteDatasourceImpl
    implements AttemptedQuestionCountRemoteDatasource {
  final FirebaseFirestore _firestore;

  AttemptedQuestionCountRemoteDatasourceImpl(
      {required FirebaseFirestore firestore})
      : _firestore = firestore;

  @override
  Future<int> getAttemptedQuestionCountForToday(String profileId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay =
        DateTime(today.year, today.month, today.day, 23, 59, 59, 999);

    final querySnapshot = await _firestore
        .collection('attempted_questions')
        .doc(profileId)
        .collection('questions')
        .where('timeStamp',
            isGreaterThanOrEqualTo: startOfDay.millisecondsSinceEpoch)
        .where('timeStamp',
            isLessThanOrEqualTo: endOfDay.millisecondsSinceEpoch)
        .where('attemptStatus', isEqualTo: true)
        .get();

    return querySnapshot.docs.length;
  }
}
