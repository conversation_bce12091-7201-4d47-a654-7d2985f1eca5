import 'package:cloud_firestore/cloud_firestore.dart';

abstract class SubscriptionConfigRemoteDataSource {
  Future<Map<String, dynamic>?> fetchSubscriptionConfig(
      String subscriptionType);
  Future<String?> fetchSubscriptionTypeByEmail(String email);
}

class SubscriptionConfigRemoteDataSourceImpl
    implements SubscriptionConfigRemoteDataSource {
  final FirebaseFirestore _firestore;
  SubscriptionConfigRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  @override
  Future<Map<String, dynamic>?> fetchSubscriptionConfig(
      String subscriptionType) async {
    try {
      final doc = await _firestore
          .collection('subscription_config')
          .doc(subscriptionType)
          .get();
      return doc.exists ? doc.data() : null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String?> fetchSubscriptionTypeByEmail(String email) async {
    try {
      final doc = await _firestore.collection('users').doc(email).get();
      if (doc.exists) {
        final data = doc.data();
        if (data != null && data.containsKey('subscriptionType')) {
          return data['subscriptionType'] as String?;
        }
      }
      return null;
    } catch (_) {
      return null;
    }
  }
}
