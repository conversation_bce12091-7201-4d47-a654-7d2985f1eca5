import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/features/questions/domain/usecases/fetch_single_question_data.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/src/experience_score/common/experience_points_enums.dart';
import 'package:skillapp/src/experience_score/domain/usecases/update_exp_points_practice.dart';
import 'package:skillapp/src/flag_question/domain/usecases/check_question_flagged.dart';
import 'package:skillapp/src/flag_question/domain/usecases/fetch_next_questionid_from_flagged_list.dart';
import 'package:skillapp/src/flag_question/domain/usecases/flag_question.dart';
import 'package:skillapp/src/flag_question/domain/usecases/remove_flag.dart';

import 'package:skillapp/src/practice/domain/usecases/fetch_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/load_next_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/populate_practice_questionbank.dart';
import 'package:skillapp/src/practice/domain/usecases/reset_question_for_retry.dart';
import 'package:skillapp/src/practice/domain/usecases/submit_practice_question.dart';
import 'package:skillapp/src/practice/domain/usecases/update_selected_option.dart';
import 'package:skillapp/src/practice/domain/usecases/util/practice_helper_util.dart';
import 'package:skillapp/src/skillpoints/domain/common/skillpoint_enums.dart';
import 'package:skillapp/src/skillpoints/domain/usecases/update_skill_points.dart';
import 'package:skillapp/src/streaks/domain/usecases/streak_day_maintenance/increment_streakday_along_with_adding_if_not_present.dart';
import 'package:skillapp/src/practice/domain/usecases/check_practice_limit_exceeded_usecase.dart';

part 'practice_event.dart';
part 'practice_state.dart';

class PracticeBloc extends Bloc<PracticeEvent, PracticeState> {
  PracticeBloc({
    required FetchPracticeQuestion fetchQuestion,
    required LoadNextPracticeQuestion loadNextQuestion,
    required SubmitPracticeQuestion submitQuestion,
    required ResetPracticeQuestionForRetry resetQuestionForRetry,
    required PopulatePracticeQuestionBank populateQuestionBank,
    required UpdateSelectedOption updateSelectedOption,
    required FlagQuestion flagQuestion,
    required RemoveFlag removeFlag,
    required CheckQuestionFlagged checkQuestionFlagged,
    required FetchSingleQuestionData fetchSingleQuestionData,
    required FetchNextQuestionIdFromFlaggedList
        fetchNextQuestionIdFromFlaggedList,
    required IncrementCurrentStreakDayAlongWithAddingIfNotPresent
        incrementCurrentStreakDayAlongWithAddingIfNotPresent,
    required UpdateSkillpoints updateSkillpoints,
    required UpdateExperienceScore updateExperienceScore,
    required CacheContext cacheContext,
    required CheckPracticeLimitExceededUseCase
        checkPracticeLimitExceededUseCase,
  })  : _cacheContext = cacheContext,
        _checkPracticeLimitExceededUseCase = checkPracticeLimitExceededUseCase,
        _fetchPracticeQuestion = fetchQuestion,
        _loadNextPracticeQuestion = loadNextQuestion,
        _submitPracticeQuestion = submitQuestion,
        _resetQuestionForRetry = resetQuestionForRetry,
        _populatePracticeQuestionBank = populateQuestionBank,
        _updateSelectedOption = updateSelectedOption,
        _flagQuestion = flagQuestion,
        _removeFlag = removeFlag,
        _checkQuestionFlagged = checkQuestionFlagged,
        _fetchSingleQuestionData = fetchSingleQuestionData,
        _fetchNextQuestionIdFromFlaggedList =
            fetchNextQuestionIdFromFlaggedList,
        _incrementCurrentStreakDayAlongWithAddingIfNotPresent =
            incrementCurrentStreakDayAlongWithAddingIfNotPresent,
        _updateSkillpoints = updateSkillpoints,
        _updateExperienceScore = updateExperienceScore,
        super(PracticeState.createEmptyState()) {
    on<LoadQuestion>(_loadQuestionEventHandler);
    on<SubmitQuestionEvent>(_submitQuestionEventHandler);
    on<SelectAnOption>(_selectAnOptionEventHandler);
    on<ResetQuestionForRetryEvent>(_resetQuestionForRetryEventHandler);
    on<NextButtonClicked>(_nextButtonClickedEventHandler);
    on<FlagUnflagQuestion>(_flagUnflagEventHandler);
    on<FetchSingleQuestionEvent>(_fetchSingleQuestionEventHandler);
  }

  final FetchPracticeQuestion _fetchPracticeQuestion;
  final LoadNextPracticeQuestion _loadNextPracticeQuestion;
  final SubmitPracticeQuestion _submitPracticeQuestion;
  final ResetPracticeQuestionForRetry _resetQuestionForRetry;
  final PopulatePracticeQuestionBank _populatePracticeQuestionBank;
  final UpdateSelectedOption _updateSelectedOption;
  final FlagQuestion _flagQuestion;
  final RemoveFlag _removeFlag;
  final CheckQuestionFlagged _checkQuestionFlagged;
  final FetchSingleQuestionData _fetchSingleQuestionData;
  final FetchNextQuestionIdFromFlaggedList _fetchNextQuestionIdFromFlaggedList;
  final IncrementCurrentStreakDayAlongWithAddingIfNotPresent
      _incrementCurrentStreakDayAlongWithAddingIfNotPresent;
  final UpdateSkillpoints _updateSkillpoints;
  final UpdateExperienceScore _updateExperienceScore;
  final CacheContext _cacheContext;
  final CheckPracticeLimitExceededUseCase _checkPracticeLimitExceededUseCase;

  Future<void> _loadQuestionEventHandler(
    LoadQuestion event,
    Emitter<PracticeState> emit,
  ) async {
    emit(PracticeQuestionLoadingState(state));
    final res = await _populatePracticeQuestionBank(event.subject);

    await res.fold(
      (failure) async {
        emit(PracticeState.createErrorState(
            errorMessage: "Unable to populate question bank"));
      },
      (success) async {
        final result = await _fetchPracticeQuestion(
          FetchPracticeQuestionParams(
              subjectId: event.subject, displayId: event.displayId),
        );

        await result.fold(
          (failure) async {
            emit(PracticeState.createErrorState(errorMessage: failure.message));
          },
          (practiceQuestion) async {
            emit(
              PracticeState(
                  questionAndAnswer: practiceQuestion.questionAndAnswer,
                  subjectId: practiceQuestion.subjectId,
                  lastAttemptStatus: AttemptStatus.notAttempted,
                  displayId: practiceQuestion.displayId,
                  currentFlow: PracticeQuestionFlows.generalPractice),
            );

            // Check if the question is flagged
            final flagResult = await _checkQuestionFlagged(
                CheckQuestionFlaggedParams(
                    practiceQuestion.questionAndAnswer.id, event.subject));

            if (!emit.isDone && flagResult.isRight()) {
              flagResult.fold(
                (_) {},
                (flaggedStatus) {
                  emit(PracticeStateWithFlaggedStatus(state, flaggedStatus));
                },
              );
            }
          },
        );
      },
    );
  }

  void _validateQuestionBuffer() {
    //Below call will check the repo for min questions and populate if needed.
    _populatePracticeQuestionBank(state.subjectId);
  }

  Future<void> _submitQuestionEventHandler(
    SubmitQuestionEvent event,
    Emitter<PracticeState> emit,
  ) async {
    // 1. Check cache for practice_limit_exceeded synchronously
    final cachedLimit = _cacheContext.get('practice_limit_exceeded');
    if (cachedLimit == true) {
      emit(PracticeState.createErrorState(
          errorMessage: "Practice limit exceeded"));
      return;
    }
    if (cachedLimit == null) {
      // 2. If not present, call use case and cache the value before submitting
      final limitResult = await _checkPracticeLimitExceededUseCase();
      limitResult.fold(
        (failure) {
          // Optionally handle error, but allow submission
          _cacheContext.set('practice_limit_exceeded', false);
        },
        (exceeded) {
          _cacheContext.set('practice_limit_exceeded', exceeded);
          if (exceeded) {
            emit(PracticeState.createErrorState(
                errorMessage: "Practice limit exceeded"));
            return;
          }
        },
      );
    }

    // 3. Submit the question as usual
    final result = await _submitPracticeQuestion(
      SubmitPracticeQuestionParams(
        question: state.questionAndAnswer,
        subjectId: state.subjectId,
        retryCount: state.retryCount,
      ),
    );

    String questionLevel = "";
    bool isSuccess = false;
    PracticeSuccessState successState = PracticeSuccessState(state);
    result.fold(
        (failure) =>
            emit(PracticeState.createErrorState(errorMessage: failure.message)),
        (practiceQuestion) {
      PracticeState resultState = state.copyWith(
        questionAndAnswer: practiceQuestion.questionAndAnswer,
        lastAttemptStatus: practiceQuestion.lastAttemptStatus,
        retryCount: practiceQuestion.retryCount,
      );
      if (practiceQuestion.lastAttemptStatus == AttemptStatus.success) {
        isSuccess = true;
        successState = PracticeSuccessState(resultState);
        emit(successState);

        //Add code to update the skillpoints
        //TODO- Should the points differ based on the attempt count?

        //Send a new state to increment CurrentDayStreakStatusProvider TODO
      } else {
        emit(resultState);
      }
      questionLevel = practiceQuestion.questionAndAnswer.complexity;
    });

    try {
      _updateExperienceScore(
        UpdateExperienceScoreParams(
            activity: ExperiencePointsActivityType.practice,
            result: isSuccess
                ? ExperiencePointsResultType.success
                : ExperiencePointsResultType.failure,
            complexity: stringToExperiencePointsLevelType(questionLevel)),
      );
    } catch (e) {
      print("Error in updating experience score: $e");
    }

    if (isSuccess) {
      _updateSkillpoints(
        UpdateSkillpointsParams(
          skillEventType: SkillEventType.practiceQuestionSuccess,
          description: "Practice question success",
        ),
      );

      await _incrementCurrentStreakDayAlongWithAddingIfNotPresent();
      emit(PracticeCountIncrementedState(successState));
    }

    // 4. After submission, if cache was present, update it asynchronously
    if (cachedLimit != null) {
      _checkPracticeLimitExceededUseCase().then((limitResult) {
        limitResult.fold(
          (failure) {
            // Optionally handle error, do not update cache
          },
          (exceeded) {
            _cacheContext.set('practice_limit_exceeded', exceeded);
          },
        );
      });
    }
  }

  Future<void> _selectAnOptionEventHandler(
    SelectAnOption event,
    Emitter<PracticeState> emit,
  ) async {
    final result = await _updateSelectedOption(
      UpdateSelectedOptionParams(
        selectedOption: event.selectedOption,
        entries: state.questionAndAnswer.answerOptions.entries,
      ),
    );

    result.fold(
      (failure) =>
          emit(PracticeState.createErrorState(errorMessage: failure.message)),
      (entries) => emit(
        state.copyWith(
            questionAndAnswer: state.questionAndAnswer.copyWith(
              answerOptions: state.questionAndAnswer.answerOptions
                  .copyWith(entries: entries),
            ),
            optionSelected: true),
      ),
    );
  }

  Future<void> _resetQuestionForRetryEventHandler(
    ResetQuestionForRetryEvent event,
    Emitter<PracticeState> emit,
  ) async {
    final result =
        await _resetQuestionForRetry(ResetPracticeQuestionForRetryParams(
      question: state.questionAndAnswer,
      retryCount: state.retryCount,
    ));

    result.fold(
      (failure) => emit(
        PracticeState.createErrorState(errorMessage: failure.message),
      ),
      (practiceQuestion) => emit(
        state.copyWith(
          questionAndAnswer: practiceQuestion.questionAndAnswer,
          retryCount: practiceQuestion.retryCount,
          lastAttemptStatus: practiceQuestion.lastAttemptStatus,
          optionSelected: practiceQuestion.optionSelected,
        ),
      ),
    );
  }

  FutureOr<void> _nextButtonClickedEventHandler(
      NextButtonClicked event, Emitter<PracticeState> emit) {
    emit(PracticeQuestionLoadingState(state));
    print(
        "AW:PracticeBloc:Next button clicked:state.currentFlow=${state.currentFlow}");
    if (state.currentFlow == PracticeQuestionFlows.generalPractice) {
      fetchNextQuestionForGeneralPractice(event, emit);
    } else if (state.currentFlow ==
        PracticeQuestionFlows.flaggedQuestionsFlow) {
      fetchNextQuestionForFlaggedFlow(event, emit);
    }
    //TODO-Add code for remaining flows as applicable
  }

  Future<void> fetchNextQuestionForFlaggedFlow(
    NextButtonClicked event,
    Emitter<PracticeState> emit,
  ) async {
    final result = await _fetchNextQuestionIdFromFlaggedList(
      FetchNextQuestionIdFromFlaggedListParams(
        state.subjectId,
        state.questionAndAnswer.id,
      ),
    );

    result.fold(
        (failure) => emit(
            PracticeState.createErrorState(errorMessage: failure.errorMessage)),
        (success) => add(FetchSingleQuestionEvent(
              success.questionId,
              success.bundleId,
              state.subjectId,
            )));
  }

  Future<void> fetchNextQuestionForGeneralPractice(
    NextButtonClicked event,
    Emitter<PracticeState> emit,
  ) async {
    // 1. Check cache for practice_limit_exceeded synchronously
    final cachedLimit = _cacheContext.get('practice_limit_exceeded');
    if (cachedLimit == true) {
      emit(PracticeState.createErrorState(
          errorMessage: "Practice limit exceeded"));
      return;
    }
    if (cachedLimit == null) {
      // 2. If not present, call use case and cache the value before loading next question
      final limitResult = await _checkPracticeLimitExceededUseCase();
      limitResult.fold(
        (failure) {
          // Optionally handle error, but allow loading
          _cacheContext.set('practice_limit_exceeded', false);
        },
        (exceeded) {
          _cacheContext.set('practice_limit_exceeded', exceeded);
          if (exceeded) {
            emit(PracticeState.createErrorState(
                errorMessage: "Practice limit exceeded"));
            return;
          }
        },
      );
    }

    final result = await _loadNextPracticeQuestion(
      LoadNextPracticeQuestionParams(
          subjectId: state.subjectId,
          questionId: state.questionAndAnswer.id,
          displayId: state.displayId,
          bundleId: state.questionAndAnswer.bundleId,
          shortDescription: state.questionAndAnswer.shortDescription,
          lastAttemptStatus: state.lastAttemptStatus),
    );

    result.fold(
      (failure) =>
          emit(PracticeState.createErrorState(errorMessage: failure.message)),
      (practiceQuestion) => emit(
        PracticeState(
            questionAndAnswer: practiceQuestion.questionAndAnswer,
            subjectId: practiceQuestion.subjectId,
            lastAttemptStatus: AttemptStatus.notAttempted,
            displayId: practiceQuestion.displayId,
            currentFlow: PracticeQuestionFlows.generalPractice),
      ),
    );

    // 3. After loading, if cache was present, update it asynchronously
    if (cachedLimit != null) {
      _checkPracticeLimitExceededUseCase().then((limitResult) {
        limitResult.fold(
          (failure) {
            // Optionally handle error, do not update cache
          },
          (exceeded) {
            _cacheContext.set('practice_limit_exceeded', exceeded);
          },
        );
      });
    }
  }

  //This can be called when single question data needs to be loaded in practice screen
  //This will be used in saved and history flows.
  FutureOr<void> _fetchSingleQuestionEventHandler(
      FetchSingleQuestionEvent event, Emitter<PracticeState> emit) async {
    try {
      final result = await _fetchSingleQuestionData(
          FetchSingleQuestionDataParams(
              questionId: event.questionId,
              bundleId: event.bundleId,
              subjectId: event.subject));

      final flagStatusResult = await _checkQuestionFlagged(
          CheckQuestionFlaggedParams(event.questionId, event.subject));

      FlaggedStatus flaggedStatus = FlaggedStatus.notFlagged;

      flagStatusResult.fold((error) => flaggedStatus = FlaggedStatus.notFlagged,
          (sucess) => flaggedStatus = sucess);

      result.fold(
          (failure) => emit(PracticeState.createErrorState(
              errorMessage: "Unable to fetch the question")),
          (success) => emit(PracticeState(
                questionAndAnswer:
                    PracticeHelperUtil.shuffleAndSetDisplayId(success),
                subjectId: event.subject,
                lastAttemptStatus: AttemptStatus.notAttempted,
                displayId: 1,
                currentFlow: state.currentFlow,
                flagValue: flaggedStatus,
              )));
    } catch (e) {
      print("AW:Exception = $e");
      emit(PracticeState.createErrorState(
          errorMessage: "Unable to fetch the question"));
    }
  }

  FutureOr<void> _flagUnflagEventHandler(
      FlagUnflagQuestion event, Emitter<PracticeState> emit) async {
    if (event.selectedFlagId == FlaggedStatus.notFlagged) {
      final result = await _removeFlag(
          RemoveFlagParams(state.questionAndAnswer.id, state.subjectId));
      result.fold(
          (failure) => emit(PracticeState.createErrorState(
              errorMessage: "Unable to remove flag for the question")),
          (success) =>
              emit(state.copyWith(flagValue: FlaggedStatus.notFlagged)));
    } else {
      //Flagging flow
      final result = await _flagQuestion(FlagQuestionParams(
          questionId: state.questionAndAnswer.id,
          bundleId: state.questionAndAnswer.bundleId,
          subjectId: state.subjectId,
          selectedFlagId: event.selectedFlagId,
          shortDescription: state.questionAndAnswer.shortDescription));

      result.fold(
          (failure) => emit(PracticeState.createErrorState(
              errorMessage: "Unable to remove flag for the question")),
          (success) => emit(state.copyWith(flagValue: event.selectedFlagId)));
    }
  }

  /*
  _loadQuestion(LoadQuestion event, Emitter<PracticeState> emit) async {
    repository.removeQuestionFromBank(
        state.subjectId, state.questionAndAnswer.id);

    //send loading event isError=loading?

//Add additional flag to show whether question is already flagged or not!
    QuestionAndAnswersModel? question =
        await repository.fetchQuestion(event.subject);

    if (question != null) {
      print("AW:_loadQuestion:shortDescription=${question.shortDescription}");
      question = _shuffleAndSetDisplayId(question);
      print("AW:_loadQuestion:shortDescription1=${question.shortDescription}");
    }

    print("AW:PracticeBloc:Loaded question= $question");
    emit(question != null
        ? PracticeState(
            questionAndAnswer: convertToQuestionAndAnswersFromModel(question),
            subjectId: event.subject,
            lastAttemptStatus: AttemptStatus.notAttempted,
            displayId: event.displayId + 1,
            currentFlow: PracticeQuestionFlows.generalPractice)
        : PracticeState.createErrorState(
            errorMessage: "Unable to fetch the question"));
    FlaggedStatus flagValue = await fetchFlaggedStatus(question, event.subject);
    emit(PracticeStateWithFlaggedStatus(state, flagValue));
    _validateQuestionBuffer();
  }


  Future<FlaggedStatus> fetchFlaggedStatus(
      QuestionAndAnswersModel? question, String subject) async {
    FlaggedStatus flagValue = FlaggedStatus.notFlagged;
    if (question != null) {
      //call checkIfQuestionIsFlagged here
      print("AW:Check if question is flagged");
      flagValue =
          await repository.checkIfQuestionIsFlagged(question.id, subject);
      print("AW:Check if question is flagged:result: $flagValue");
      //write code to emit the state with flag value
    }
    return flagValue;
  }

  //create new method to fetch history questions!

  FutureOr<void> _submitQuestion(
      SubmitQuestionEvent event, Emitter<PracticeState> emit) {
    QuestionAndAnswersModel question =
        convertQuestionAndAnswersToDataModel(state.questionAndAnswer);
    String selectedAnswer = question.answerOptions.entries
        .firstWhere(
            (element) => element.status == AnswerOptionStatuses.selected)
        .id;

    bool isAnswerCorrect = question.correctAnswer == selectedAnswer;
    int currentRetryCount = state.retryCount + 1;
    //update the history
    repository.addToAttemptHistory(
        state.questionAndAnswer.id,
        state.questionAndAnswer.bundleId,
        isAnswerCorrect ? AttemptStatus.success : AttemptStatus.failure,
        state.questionAndAnswer.shortDescription,
        state.subjectId);

    //Remove the question if answer is correct or user tried maximum failure attempts allowed.
    if (isAnswerCorrect ||
        (!isAnswerCorrect && currentRetryCount > UIParameters.maxRetryCount)) {
      repository.removeQuestionFromBank(
          state.subjectId, state.questionAndAnswer.id);
      repository.markAsAttemptedInUserProfile(state.subjectId,
          state.questionAndAnswer.bundleId, state.questionAndAnswer.id);
    }

    //Consider case where both right and wrong can come!!
    //return state

    QuestionAndAnswersModel updatedQuestion = question.copyWith(
        answerOptions: question.answerOptions.copyWith(
      entries: question.answerOptions.entries
          .map(
            (e) => _updateOptionStatus(
                e.id == selectedAnswer
                    ? (isAnswerCorrect
                        ? AnswerOptionStatuses.rightAnswer
                        : AnswerOptionStatuses.wrongAnswer)
                    : AnswerOptionStatuses.notSelected,
                e),
          )
          .toList()
          .cast<OptionEntryModel>(),
    ));

    emit(
      state.copyWith(
        questionAndAnswer:
            convertToQuestionAndAnswersFromModel(updatedQuestion),
        lastAttemptStatus: isAnswerCorrect
            ? AttemptStatus.success
            : (currentRetryCount > UIParameters.maxRetryCount
                ? AttemptStatus.lastAttemptDone
                : AttemptStatus.failure),
        retryCount: currentRetryCount,
      ),
    );
  }
*/
  /*FutureOr<void> _resetQuestionForRetry(
      ResetQuestionForRetryEvent event, Emitter<PracticeState> emit) {
    QuestionAndAnswersModel question =
        convertQuestionAndAnswersToDataModel(state.questionAndAnswer);

    //return state by resetting all the options to notselected and isubmitted flag is false
    QuestionAndAnswersModel updatedQuestion = question.copyWith(
        answerOptions: question.answerOptions.copyWith(
      entries: question.answerOptions.entries
          .map((e) => _updateOptionStatus(AnswerOptionStatuses.notSelected, e))
          .toList()
          .cast<OptionEntryModel>(),
    ));

    emit(
      state.copyWith(
        questionAndAnswer: convertToQuestionAndAnswersFromModel(
            _shuffleAndSetDisplayId(updatedQuestion)),
        retryCount: state.retryCount + 1,
        lastAttemptStatus: AttemptStatus.retryingAfterFailure,
        optionSelected: false,
      ),
    );
  }*/

  /*FutureOr<void> _updateSelectedOption(
      SelectAnOption event, Emitter<PracticeState> emit) {
    QuestionAndAnswersModel question =
        convertQuestionAndAnswersToDataModel(state.questionAndAnswer);

    //Return this as a state
    QuestionAndAnswersModel updatedQuestion = question.copyWith(
        answerOptions: question.answerOptions.copyWith(
      entries: question.answerOptions.entries
          .map((e) => _updateOptionStatus(
              e.id == event.selectedOption
                  ? AnswerOptionStatuses.selected
                  : AnswerOptionStatuses.notSelected,
              e))
          .toList()
          .cast<OptionEntryModel>(),
    ));

    emit(
      state.copyWith(
        questionAndAnswer:
            convertToQuestionAndAnswersFromModel(updatedQuestion),
        optionSelected: true,
      ),
    );
  }

  OptionEntryModel _updateOptionStatus(
      AnswerOptionStatuses status, OptionEntryModel optionEntry) {
    return optionEntry.copyWith(status: status);
  }

  //This method will shuffle and set the display id.
  QuestionAndAnswersModel _shuffleAndSetDisplayId(
      QuestionAndAnswersModel question) {
    List<String> optionIdList =
        question.answerOptions.entries.map((e) => e.id).toList();
    optionIdList.shuffle();
    List<OptionEntryModel> optionsList = question.answerOptions.entries
        .map(
          (e) => e.copyWith(
            displayId: optionIdList.removeLast(),
          ),
        )
        .toList();

    optionsList.sort(
      (a, b) => a.displayId.compareTo(b.displayId),
    );
    QuestionAndAnswersModel q = question.copyWith(
        answerOptions: question.answerOptions.copyWith(entries: optionsList));
    return q;
  }

  void _validateQuestionBuffer() {
    //Below call will check the repo for min questions and populate if needed.
    repository.populateQuestionBank();
  }

  FutureOr<void> _processNextButtonClickEvent(
      NextButtonClicked event, Emitter<PracticeState> emit) {
    print(
        "AW:PracticeBloc:Next button clicked:state.currentFlow=${state.currentFlow}");
    if (state.currentFlow == PracticeQuestionFlows.generalPractice) {
      fetchNextQuestionForGeneralPractice(state);
    } else if (state.currentFlow ==
        PracticeQuestionFlows.flaggedQuestionsFlow) {
      fetchNextQuestionForFlaggedFlow(state);
    }
  }

  void fetchNextQuestionForGeneralPractice(PracticeState state) {
    //This will remove the question from local queue if it is still present
    repository.removeQuestionFromBank(
        state.subjectId, state.questionAndAnswer.id);

    if (state.lastAttemptStatus == AttemptStatus.notAttempted) {
      repository.markAsAttemptedInUserProfile(state.subjectId,
          state.questionAndAnswer.bundleId, state.questionAndAnswer.id);

      repository.addToAttemptHistory(
          state.questionAndAnswer.id,
          state.questionAndAnswer.bundleId,
          AttemptStatus.notAttempted,
          state.questionAndAnswer.shortDescription,
          state.subjectId);
    }

    add(
      LoadQuestion(
        subject: state.subjectId,
        displayId: state.displayId,
      ),
    );
  }

  fetchNextQuestionForFlaggedFlow(PracticeState state) {
    //TODO write code here to fetch next question for flagged flow
    FlaggedQuestionModel? nextQuestionData = repository.getNextFlaggedQuestion(
        state.subjectId, state.questionAndAnswer.id);
    if (nextQuestionData != null) {
      print("Adding next question fetch event $nextQuestionData");
      add(FetchSingleFlaggedQuestionEvent(
        nextQuestionData.question.id,
        nextQuestionData.question.bundleId,
        state.subjectId,
      ));
    } else {
      print("No more saved questions found");
      emit(PracticeState.createErrorState(
          errorMessage: "No more saved questions found"));
    }
  }


  FutureOr<void> _flagQuestionClicked(
      FlagUnflagQuestion event, Emitter<PracticeState> emit) {
    if (event.selectedFlagId == FlaggedStatus.notFlagged) {
      //UnFlagging flow
      repository.removeFlagForQuestion(
          state.questionAndAnswer.id, state.subjectId);
      emit(state.copyWith(flagValue: FlaggedStatus.notFlagged));
    } else {
      //Flagging flow
      repository.flagQuestion(
          state.questionAndAnswer.id,
          state.questionAndAnswer.bundleId,
          state.subjectId,
          event.selectedFlagId,
          state.questionAndAnswer.shortDescription);
      emit(state.copyWith(flagValue: event.selectedFlagId));
    }
  }
  FutureOr<void> _fetchFlaggedQuestionData(
      FetchSingleFlaggedQuestionEvent event,
      Emitter<PracticeState> emit) async {
    await fetchQuestionData(event, emit);
  }

  FutureOr<void> _fetchHistoryQuestionData(
      FetchSingleHistoryQuestionEvent event,
      Emitter<PracticeState> emit) async {
    await fetchQuestionData(event, emit);
  }

  Future<void> fetchQuestionData(
      FetchSingleQuestionEvent event, Emitter<PracticeState> emit) async {
    try {
      print(
          "Inside FechSingleFlaggedQuestionEvent ${event.questionId}, ${event.bundleId}, ${event.subject}");
      QuestionAndAnswersModel question =
          await repository.fetchSingleQuestionData(
              event.questionId, event.bundleId, event.subject);
      print("question fetched ${question.id}");
      question = _shuffleAndSetDisplayId(question);
      print("AW:PracticeBloc:Loaded question= $question");
      emit(
        PracticeState(
          questionAndAnswer: convertToQuestionAndAnswersFromModel(question),
          subjectId: event.subject,
          lastAttemptStatus: AttemptStatus.notAttempted,
          displayId: 1,
          currentFlow: PracticeQuestionFlows.flaggedQuestionsFlow,
        ),
      );

      FlaggedStatus flagValue =
          await fetchFlaggedStatus(question, event.subject);
      emit(PracticeStateWithFlaggedStatus(state, flagValue));
    } catch (e) {
      print("AW:Exception = $e");
      emit(PracticeState.createErrorState(
          errorMessage: "Unable to fetch the question"));
    }
  }*/
}

/*
Submit action:
      If answer is correct,
              - Add as attempted in user profile. (This remove the question from getting fetched again)
              - Remove from current Question bank Map
              - Add to history as success attempt

      If answer is not correct,
              - Add to history as failure attempt

Click of Next button:
      If last attempt was success,
              - Fetch question
      If last attempt was failure,
              - Add as attempted in user profile.
              - Remove from current Question bank Map
              - Fetch question
      If last attempt was not submitted,
              - Add as attempted in user profile.
              - Remove from current Question bank Map
              - Add to history as skipped
              - Fetch question
*/
