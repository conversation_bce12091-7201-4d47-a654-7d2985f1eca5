import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/common/features/questions/data/models/question.dart';
import 'package:skillapp/src/home/<USER>/models/subjects_model.dart';
import 'package:skillapp/src/practice/data/models/attempted_bundles.dart';

abstract class PracticeQuestionBankRemoteDataSource {
  const PracticeQuestionBankRemoteDataSource();

  Future<Set<String>> getBundleList(String subjectId, int level);

  Future<AttemptedBundleDetailsModel> getAttemptedBundles(
      String profileId, String email, String subjectId);

  Future<Set<QuestionModel>> getQuestionsFromBundle(
      String subjectId, String bundleId);

  Future<Set<QuestionModel>> getQuestionIdsFromBundle(
      String subjectId, String bundleId);

  Future<Set<String>> getAttemptedQuestionsFromBundle(
      String profileId, String email, String bundleId, String subjectId);

  Future<void> addToAttemptHistory(
      String profileId,
      String questionId,
      String bundleId,
      AttemptStatus attemptStatus,
      String shortDescription,
      String subjectId);

  Future<void> markAsAttemptedInUserProfile(String profileId, String email,
      String subjectId, String bundleId, String questionId);
}

class QuestionBankRemoteDataSourceImpl
    implements PracticeQuestionBankRemoteDataSource {
  const QuestionBankRemoteDataSourceImpl({required FirebaseFirestore firestore})
      : _firestore = firestore;

  final FirebaseFirestore _firestore;

  @override
  Future<Set<String>> getBundleList(String subjectId, int level) async {
    try {
      print("AW:_fetchQuestionBundles:subjectId= $subjectId, level= $level");
      QuerySnapshot questionBundleSnapshot = await _firestore
          .collection('subjects')
          .doc(subjectId)
          .collection('question_bundles')
          .where('level', isEqualTo: level)
          .get();

      Set<String> questionBundleList =
          questionBundleSnapshot.docs.map((questionBundleDocument) {
        return questionBundleDocument.id;
      }).toSet();

      print(
          "AW:_fetchQuestionBundles:questionBundleList= $questionBundleList, subjectId= $subjectId");

      return questionBundleList;
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<AttemptedBundleDetailsModel> getAttemptedBundles(
      String profileId, String email, String subjectId) async {
    try {
      print("AW:_fetchInProgressBundles:profileId= $profileId");
      print("AW:_fetchInProgressBundles:email= $email");

      QuerySnapshot<Map<String, dynamic>> inProgressSnapshot = await _firestore
          .collection(
              'users/$email/profiles/$profileId/attempted_bundles/$subjectId/bundles')
          .get();

      print("JB ccheck!!");

      AttemptedBundleDetailsModel attemptedQuestionDetails =
          AttemptedBundleDetailsModel();

      attemptedQuestionDetails.attemptedBundleIds = inProgressSnapshot.docs
          .map((doc) => QuestionBundleModel(
              id: doc.id, isCompleted: doc.data()['completed'] ?? false))
          .toSet();
      //TODO- Server side code to mark a bundle as completed is pending!

      for (var bundle in attemptedQuestionDetails.attemptedBundleIds) {
        bundle.isCompleted
            ? attemptedQuestionDetails.completedBundleIds.add(bundle.id)
            : attemptedQuestionDetails.inprogressBundleIds.add(bundle.id);
      }
      print(
          "AW:_fetchAttemptedBundles:attemptedQuestionDetails= $attemptedQuestionDetails");
      return attemptedQuestionDetails;
    } on FirebaseException catch (e, s) {
      debugPrintStack(stackTrace: s);
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<Set<QuestionModel>> getQuestionsFromBundle(
      String subjectId, String bundleId) async {
    try {
      print(
          "AW:_fetchQuestionsFromBundle:subjectId = $subjectId & bundleId= $bundleId");

      Set<QuestionModel> questionIdSet =
          await getQuestionIdsFromBundle(subjectId, bundleId);

      Set<QuestionModel> questionSet = <QuestionModel>{};

      //TODO- This logic to be changed to "wherein" to improve performance.
      for (var question in questionIdSet) {
        DocumentSnapshot<Map<String, dynamic>> questionSnapshot =
            await _firestore
                .collection('subjects/$subjectId/questions')
                .doc(question.id)
                .get();
        questionSet.add(QuestionModel(
            data: questionSnapshot.data()!['data'],
            id: question.id,
            bundleId: question.bundleId));
      }
      return questionSet;
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<Set<QuestionModel>> getQuestionIdsFromBundle(
      String subjectId, String bundleId) async {
    try {
      print(
          "AW:_fetchQuestionsFromBundle:subjectId = $subjectId & bundleId= $bundleId");
      QuerySnapshot<Map<String, dynamic>> questionsSnapshot = await _firestore
          .collection(
              'subjects/$subjectId/question_bundles/$bundleId/question_ids')
          .get();
      return questionsSnapshot.docs.map((doc) {
        //Map<String, dynamic> data = doc.data();
        return QuestionModel(id: doc.id, data: '', bundleId: bundleId);
      }).toSet();
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<Set<String>> getAttemptedQuestionsFromBundle(
      String profileId, String email, String bundleId, String subjectId) async {
    try {
      print(
          "AW:_fetchAttemptedQuestionsFromBundle:profileId=$profileId, email=$email, subjectId=$subjectId, bundleId=$bundleId");
      QuerySnapshot<
          Map<String,
              dynamic>> attemptedQuestionsSnapshot = await _firestore
          .collection(
              'users/$email/profiles/$profileId/attempted_bundles/$subjectId/bundles/$bundleId/questions')
          .get();
      return attemptedQuestionsSnapshot.docs.map((doc) => doc.id).toSet();
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<void> addToAttemptHistory(
      String profileId,
      String questionId,
      String bundleId,
      AttemptStatus attemptStatus,
      String shortDescription,
      String subjectId) async {
    //TODO-Check if below two checks to see if the documents exists can be avoided.
    print(
        "AW:addToAttemptHistory:questionId= $questionId, bundleId= $bundleId, attemptStatus= $attemptStatus, shortDescription= $shortDescription");

    try {
      CollectionReference attemptedQuestionsCollection =
          _firestore.collection('attempted_questions');

      DocumentSnapshot profileSnapshot =
          await attemptedQuestionsCollection.doc().get();
      if (!profileSnapshot.exists) {
        attemptedQuestionsCollection.doc(profileId).set({'defaultValue': ''});
      }

      _firestore
          .collection('attempted_questions/$profileId/questions')
          .doc()
          .set(
        {
          'questionId': questionId,
          'bundleId': bundleId,
          'subjectId': subjectId,
          'shortDescription': shortDescription,
          'attemptStatus': attemptStatus.name,
          'timeStamp': DateTime.now()
        },
      );
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }

  @override
  Future<void> markAsAttemptedInUserProfile(String profileId, String email,
      String subjectId, String bundleId, String questionId) async {
    try {
      CollectionReference attemptedBundlesCollection = _firestore
          .collection('users/$email/profiles/$profileId/attempted_bundles');

      DocumentSnapshot subjectSnapshot =
          await attemptedBundlesCollection.doc(subjectId).get();
      if (!subjectSnapshot.exists) {
        attemptedBundlesCollection.doc(subjectId).set({'defaultValue': ''});
      }

      CollectionReference bundlesCollection = _firestore.collection(
          'users/$email/profiles/$profileId/attempted_bundles/$subjectId/bundles');

      DocumentSnapshot bundleIdSnapshot =
          await bundlesCollection.doc(bundleId).get();
      if (!bundleIdSnapshot.exists) {
        bundlesCollection.doc(bundleId).set({'defaultValue': ''});
      }

      _firestore
          .collection(
              'users/$email/profiles/$profileId/attempted_bundles/$subjectId/bundles/$bundleId/questions')
          .doc(questionId)
          .set({"randomField": "randomValue"});
    } on FirebaseException catch (e) {
      throw ServerException(
        message: e.message ?? 'Unknown error',
        statusCode: e.code,
      );
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(
        message: e.toString(),
        statusCode: '500',
      );
    }
  }
}
