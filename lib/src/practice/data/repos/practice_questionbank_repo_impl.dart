import 'package:dartz/dartz.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/exceptions.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/practice/data/datasources/practice_questionbank_remote_datasource.dart';
import 'package:skillapp/core/common/features/questions/data/repos/util/question_repo_util.dart';
import 'package:skillapp/src/practice/domain/entities/attempted_question.dart';
import 'package:skillapp/src/practice/domain/repos/practice_questionbank_repo.dart';

class PracticeQuestionBankRepoImpl implements PracticeQuestionBankRepo {
  PracticeQuestionBankRepoImpl({
    required PracticeQuestionBankRemoteDataSource questionBankRemoteDataSource,
    required QuestionDataUtil questionDataUtil,
    required CacheContext cacheContext,
  })  : _questionBankRemoteDataSource = questionBankRemoteDataSource,
        _questionDataUtil = questionDataUtil,
        _cacheContext = cacheContext;

  final PracticeQuestionBankRemoteDataSource _questionBankRemoteDataSource;
  final QuestionDataUtil _questionDataUtil;
  final CacheContext _cacheContext;

  @override
  ResultFuture<Set<String>> getBundleList(String subjectId) async {
    try {
      final result = await _questionBankRemoteDataSource.getBundleList(
          subjectId, _cacheContext.level);

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<AttemptedBundleDetails> getAttemptedBundles(
      String subjectId) async {
    print("JB:subjectId = $subjectId");
    try {
      final result = await _questionBankRemoteDataSource.getAttemptedBundles(
          _cacheContext.profileId, _cacheContext.email, subjectId);

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<Set<QuestionAndAnswers>> getQuestionsFromBundle(
      String subjectId, String bundleId) async {
    try {
      final result = await _questionBankRemoteDataSource.getQuestionsFromBundle(
          subjectId, bundleId);

      return Right(
        result
            .map((e) => _questionDataUtil.convertQuestionModelToEntity(e))
            .toSet(),
      );
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<Set<String>> getAttemptedQuestionsFromBundle(
      String subjectId, String bundleId) async {
    try {
      final result =
          await _questionBankRemoteDataSource.getAttemptedQuestionsFromBundle(
              _cacheContext.profileId,
              _cacheContext.email,
              bundleId,
              subjectId);

      return Right(result);
    } on ServerException catch (e) {
      print("JB:Unable to fetch attempted questions from bundleId: $bundleId");
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<bool> addToAttemptHistory(
      String id,
      String bundleId,
      AttemptStatus attemptStatus,
      String shortDescription,
      String subjectId) async {
    try {
      _questionBankRemoteDataSource.addToAttemptHistory(_cacheContext.profileId,
          id, bundleId, attemptStatus, shortDescription, subjectId);
      return const Right(true);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<void> markAsAttemptedInUserProfile(
      String subjectId, String bundleId, String questionId) async {
    try {
      final result = _questionBankRemoteDataSource.markAsAttemptedInUserProfile(
          _cacheContext.profileId,
          _cacheContext.email,
          subjectId,
          bundleId,
          questionId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }
}
