import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/entities/answer_options.dart';
import 'package:skillapp/core/common/entities/question.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/practice/domain/entities/practice_question.dart';
import 'package:skillapp/src/practice/domain/repos/practice_questionbank_repo.dart';
import 'package:skillapp/src/practice/domain/usecases/util/practice_helper_util.dart';

class SubmitPracticeQuestion extends FutureUsecaseWithParams<PracticeQuestion,
    SubmitPracticeQuestionParams> {
  final PracticeQuestionBankRepo _questionBankRepo;
  final CacheContext _cacheContext;

  SubmitPracticeQuestion(
      {required PracticeQuestionBankRepo questionBankRepo,
      required CacheContext cacheContext})
      : _questionBankRepo = questionBankRepo,
        _cacheContext = cacheContext;

  @override
  ResultFuture<PracticeQuestion> call(
      SubmitPracticeQuestionParams params) async {
    try {
      //PracticeQuestion practiceQuestion = params;
      QuestionAndAnswers question = params.question;
      String selectedAnswer = question.answerOptions.entries
          .firstWhere(
              (element) => element.status == AnswerOptionStatuses.selected)
          .id;

      bool isAnswerCorrect = question.correctAnswer == selectedAnswer;
      int currentRetryCount = params.retryCount + 1;
      //update the history
      _questionBankRepo.addToAttemptHistory(
          question.id,
          question.bundleId,
          isAnswerCorrect ? AttemptStatus.success : AttemptStatus.failure,
          question.shortDescription,
          params.subjectId);

      //Remove the question if answer is correct or user tried maximum failure attempts allowed.
      if (isAnswerCorrect ||
          (!isAnswerCorrect &&
              currentRetryCount > PracticeHelperUtil.kMaxRetryCount)) {
        _cacheContext.removeQuestionFromQuestionBank(
            params.subjectId, question.id);

        _questionBankRepo.markAsAttemptedInUserProfile(
            params.subjectId, question.bundleId, question.id);
      }

      //Consider case where both right and wrong can come!!
      //return state

      QuestionAndAnswers updatedQuestion = question.copyWith(
          answerOptions: question.answerOptions.copyWith(
        entries: question.answerOptions.entries
            .map(
              (e) => PracticeHelperUtil.updateOptionStatus(
                  e.id == selectedAnswer
                      ? (isAnswerCorrect
                          ? AnswerOptionStatuses.rightAnswer
                          : AnswerOptionStatuses.wrongAnswer)
                      : AnswerOptionStatuses.notSelected,
                  e),
            )
            .toList()
            .cast<OptionEntry>(),
      ));

      return Right(
        PracticeQuestion(
          questionAndAnswer: updatedQuestion,
          lastAttemptStatus: isAnswerCorrect
              ? AttemptStatus.success
              : (currentRetryCount > PracticeHelperUtil.kMaxRetryCount
                  ? AttemptStatus.lastAttemptDone
                  : AttemptStatus.failure),
          retryCount: currentRetryCount,
        ),
      );
    } catch (e) {
      debugPrint(e.toString());
      return const Left(
          ServerFailure(message: "Unable to load questions", statusCode: 500));
    }
  }
}

class SubmitPracticeQuestionParams extends Equatable {
  final QuestionAndAnswers question;
  final String subjectId;
  final int retryCount;

  const SubmitPracticeQuestionParams(
      {required this.question,
      required this.subjectId,
      required this.retryCount});

  @override
  List<Object?> get props => [question.id, subjectId];
}
