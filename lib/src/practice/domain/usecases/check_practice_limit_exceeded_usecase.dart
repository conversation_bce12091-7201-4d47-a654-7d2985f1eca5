import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:dartz/dartz.dart';
import 'package:skillapp/src/subscription_config/domain/entities/subscription_config.dart';
import 'package:skillapp/src/subscription_config/domain/usecases/fetch_and_cache_subscription_config.dart';
import 'package:skillapp/src/subscription_config/domain/usecases/fetch_attempted_question_count_usecase.dart';

/// Use case to check if user exceeded allowed practice limit
class CheckPracticeLimitExceededUseCase
    extends FutureUsecaseWithoutParams<bool> {
  final FetchAttemptedQuestionCountUseCase _fetchAttemptedCount;
  final FetchAndCacheSubscriptionConfigUseCase _fetchConfig;

  CheckPracticeLimitExceededUseCase({
    required FetchAttemptedQuestionCountUseCase fetchAttemptedCount,
    required FetchAndCacheSubscriptionConfigUseCase fetchConfig,
  })  : _fetchAttemptedCount = fetchAttemptedCount,
        _fetchConfig = fetchConfig;

  @override
  ResultFuture<bool> call() async {
    try {
      // 1. Fetch attempted count
      final attemptedResult = await _fetchAttemptedCount();
      if (attemptedResult.isLeft()) {
        return Left(attemptedResult.fold((l) => l,
            (_) => const ServerFailure(message: 'Unknown', statusCode: 500)));
      }
      final attemptedCount = attemptedResult.getOrElse(() => 0);

      // 2. Fetch subscription config
      final configResult = await _fetchConfig();
      if (configResult.isLeft()) {
        return Left(configResult.fold((l) => l,
            (_) => const ServerFailure(message: 'Unknown', statusCode: 500)));
      }
      final SubscriptionConfig config = configResult.getOrElse(() =>
          throw const ServerFailure(
              message: 'Config missing', statusCode: 500));

      // 3. Compare counts
      final allowed = config.maxDailyPracticeCount;
      final exceeded = attemptedCount >= allowed;
      return Right(exceeded);
    } catch (e) {
      return const Left(ServerFailure(
          message: 'Unknown error checking practice limit', statusCode: 500));
    }
  }
}
