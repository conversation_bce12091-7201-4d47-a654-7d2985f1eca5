import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:skillapp/core/common/cache/context_cache.dart';
import 'package:skillapp/core/common/usecase/usecase.dart';
import 'package:skillapp/core/common/utils/typedefs.dart';
import 'package:skillapp/core/enums/enum_master.dart';
import 'package:skillapp/core/errors/failures.dart';
import 'package:skillapp/src/practice/domain/entities/practice_question.dart';
import 'package:skillapp/src/practice/domain/repos/practice_questionbank_repo.dart';
import 'package:skillapp/src/practice/domain/usecases/fetch_practice_question.dart';

class LoadNextPracticeQuestion extends FutureUsecaseWithParams<PracticeQuestion,
    LoadNextPracticeQuestionParams> {
  final PracticeQuestionBankRepo _bankRepo;
  final FetchPracticeQuestion _fetchQuestion;
  final CacheContext _cacheContext;

  LoadNextPracticeQuestion({
    required PracticeQuestionBankRepo bankRepo,
    required FetchPracticeQuestion fetchQuestion,
    required CacheContext cacheContext,
  })  : _bankRepo = bankRepo,
        _fetchQuestion = fetchQuestion,
        _cacheContext = cacheContext;

  @override
  ResultFuture<PracticeQuestion> call(
      LoadNextPracticeQuestionParams params) async {
    try {
      _cacheContext.removeQuestionFromQuestionBank(
          params.subjectId, params.questionId);

      if (params.lastAttemptStatus == AttemptStatus.notAttempted) {
        _bankRepo.markAsAttemptedInUserProfile(
            params.subjectId, params.bundleId, params.questionId);

        _bankRepo.addToAttemptHistory(
            params.questionId,
            params.bundleId,
            AttemptStatus.notAttempted,
            params.shortDescription,
            params.subjectId);
      }

      return _fetchQuestion(
        FetchPracticeQuestionParams(
          subjectId: params.subjectId,
          displayId: params.displayId,
        ),
      );
    } catch (e) {
      debugPrint(e.toString());
      return const Left(
          ServerFailure(message: "Unknown error", statusCode: 500));
    }
  }
}

class LoadNextPracticeQuestionParams extends Equatable {
  final String subjectId;
  final String questionId;
  final int displayId;
  final String bundleId;
  final String shortDescription;
  final AttemptStatus lastAttemptStatus;

  const LoadNextPracticeQuestionParams({
    required this.subjectId,
    required this.questionId,
    required this.displayId,
    required this.bundleId,
    required this.shortDescription,
    required this.lastAttemptStatus,
  });

  @override
  List<Object?> get props => [
        subjectId,
        questionId,
        displayId,
        bundleId,
        shortDescription,
        lastAttemptStatus
      ];
}
