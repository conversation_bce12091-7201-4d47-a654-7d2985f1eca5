import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/themes/app_colors.dart';

class SubjectCardWidget extends StatelessWidget {
  final String title;
  final String description;
  final String imageAsset;
  final Color backgroundColor;
  final VoidCallback onStartPressed;
  final double? height;
  final bool showComingSoon;

  const SubjectCardWidget({
    super.key,
    required this.title,
    required this.description,
    required this.imageAsset,
    required this.backgroundColor,
    required this.onStartPressed,
    this.height,
    this.showComingSoon = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: const [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 10,
            offset: Offset(0, 0),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top image section with colored background
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Center(
              child: Image.asset(
                imageAsset,
                height: 80,
                fit: BoxFit.contain,
              ),
            ),
          ),
          // Content section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  constraints: const BoxConstraints(minHeight: 60),
                  child: Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ),
                showComingSoon
                    ? const SizedBox(height: 12)
                    : const SizedBox(height: 80),
                // Start button
                if (!showComingSoon)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: onStartPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF50409A),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Start'),
                    ),
                  )
                else
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          () {}, // Empty function instead of null to avoid disabled styling
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: kPrimaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side:
                              const BorderSide(width: 1, color: kPrimaryColor),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 10),
                      ),
                      child: const Text(
                        'Coming Soon',
                        style: TextStyle(
                          color: kPrimaryColor,
                          fontSize: 16,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                          height: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
