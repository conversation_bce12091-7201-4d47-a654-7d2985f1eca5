import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:skillapp/core/common/utils/screen_Util.dart';
import 'package:skillapp/core/configs/configs.dart';
import 'package:skillapp/src/home/<USER>/blocs/dashboard_summary/dashboard_summary_bloc.dart';
import 'package:skillapp/src/home/<USER>/widgets/learning_badge_name.dart';
import 'package:skillapp/src/home/<USER>/widgets/learning_image_icon.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/web_daily_progress.dart';
import 'package:skillapp/src/streaks/presentation/providers/current_day_streak_status_provider.dart';

class LearningInfo extends StatefulWidget {
  const LearningInfo({super.key});

  @override
  State<StatefulWidget> createState() => _LearningInfoState();
}

class _LearningInfoState extends State<LearningInfo> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardSummary();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    /* final dashboardBloc = context.read<DashboardSummaryBloc>();
    if (!dashboardBloc.isClosed) {
      dashboardBloc.add(GetDashboardSummary());
    }*/
  }

  @override
  void didUpdateWidget(LearningInfo oldWidget) {
    super.didUpdateWidget(oldWidget);
    _loadDashboardSummary();
  }

  void _loadDashboardSummary() {
    final dashboardBloc = context.read<DashboardSummaryBloc>();
    if (!dashboardBloc.isClosed) {
      // Force a reset first
      dashboardBloc.add(ResetDashboardSummary());
      // Then fetch new data
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!dashboardBloc.isClosed) {
          dashboardBloc.add(GetDashboardSummary());
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double fem = ScreenUtil.getFem(context);
    double hfem = ScreenUtil.getHfem(context);

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          // margin: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 16 * fem, 16 * hfem),
          // padding: EdgeInsets.fromLTRB(16 * fem, 0 * fem, 20 * fem, 16),
          padding: EdgeInsets.fromLTRB(16, 0 * fem, 20, 16),
          height: 340,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20 * fem),
            gradient: const LinearGradient(
              begin: Alignment(0.00, -1.00),
              end: Alignment(0, 1),
              colors: [Color(0xFF50409A), Color(0xFF08002E)],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Component for displaying image and badge name
              Container(
                //  margin: EdgeInsets.fromLTRB(12 * fem, 28 * hfem, 11 * fem, 28 * hfem),
                margin: EdgeInsets.fromLTRB(12 * fem, 28, 11 * fem, 28),
                // margin: EdgeInsets.fromLTRB(12, 28, 11, 28),
                width: double.infinity,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(
                          0 * fem, 16 * fem, 61 * fem, 0 * fem),
                      child: const Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          LearningImageIconWidget(),
                          LearningBadgeNameWidget(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                // padding: EdgeInsets.fromLTRB(16 * fem, 2 * hfem, 2 * fem, 4 * hfem),
                padding: EdgeInsets.fromLTRB(16 * fem, 2, 2 * fem, 4),
                //  width: MediaQuery.of(context).size.width * 0.20,
                // width: 400 * fem,
                width: math.min(400 * fem, 400),
                child:
                    BlocConsumer<DashboardSummaryBloc, DashboardSummaryState>(
                        listener: (context, state) {
                  // This will be called whenever the state changes
                  print(
                      'LearningInfo: BlocConsumer listener called with state: ${state.runtimeType}');
                  if (state is DashboardSummaryLoaded) {
                    print(
                        'LearningInfo: Loaded state with data - XP: ${state.dashboardSummary.xpPoints}, Streak: ${state.dashboardSummary.streakDays}');
                  }
                }, builder: (context, state) {
                  if (state is DashboardSummaryLoading) {
                    // return const CircularProgressIndicator();
                    return Container();
                  }
                  if (state is DashboardSummaryLoaded) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.fromLTRB(
                              16 * fem, 0 * hfem, 0 * fem, 0 * hfem),
                          width: MediaQuery.of(context).size.width * 0.18,
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                width: 1,
                                strokeAlign: BorderSide.strokeAlignCenter,
                                color: Colors.white
                                    .withOpacity(0.20000000298023224),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          // height: 20 * hfem,
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('Streak',
                                    style: kLearningInfoMetricLabelTs),
                                //  SizedBox(height: 6 * hfem),
                                const SizedBox(height: 6),
                                Text(
                                  //  '100 days',
                                  state.dashboardSummary.streakDays,
                                  style: kLearningInfoMetricValueTs,
                                )
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Text('XP',
                                        style: kLearningInfoMetricLabelTs),
                                    SizedBox(
                                      width: 4 * fem,
                                    ),
                                    Image.asset(
                                        'assets/images/dashboard/xp_logo.png')
                                  ],
                                ),
                                //  SizedBox(height: 6 * hfem),
                                const SizedBox(height: 6),
                                Text(
                                    //'23',
                                    state.dashboardSummary.xpPoints.toString(),
                                    style: kLearningInfoMetricValueTs)
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Text('Coins',
                                        style: kLearningInfoMetricLabelTs),
                                    SizedBox(
                                      width: 4 * fem,
                                    ),
                                    Image.asset(
                                        'assets/images/dashboard/ninjacoin_logo.png'),
                                  ],
                                ),
                                //   SizedBox(height: 6 * hfem),
                                const SizedBox(height: 6),
                                Text(
                                  //   '43',
                                  state.dashboardSummary.skillPoints.toString(),
                                  style: kLearningInfoMetricValueTs,
                                )
                              ],
                            )
                          ],
                        ),
                        const SizedBox(
                          //   height: 20 * hfem,
                          height: 20,
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width * 0.18,
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                width: 1,
                                strokeAlign: BorderSide.strokeAlignCenter,
                                color: Colors.white
                                    .withOpacity(0.20000000298023224),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 14,
                        ),
                      ],
                    );
                  } else {
                    return Container();
                  }
                }),
              ),
            ],
          ),
        ),
        Positioned(
            right: 20 * fem,
            bottom: 0,
            child: Image.asset(
              'assets/images/web/web_blueNinja.png',
              //  height: MediaQuery.of(context).size.height * 0.365,
              height: 365,
              fit: BoxFit.contain,
            )
            // child: S vgPicture.asset('assets/images/web/web_blueNinja.svg'),
            ),
        Positioned(
          bottom: 0 * hfem,
          right: 0 * fem,
          left: 0 * fem,
          // top: 10,
          child: Consumer<CurrentDayStreakStatusProvider>(
            builder: (_, provider, __) {
              if (provider.currentDayStreakStatus != null) {
                int targetCount =
                    provider.currentDayStreakStatus?.targetCount ?? 0;
                int completedCount =
                    provider.currentDayStreakStatus?.completedCount ?? 0;

                double completedPercent = (completedCount / targetCount);
                int percentToShow = (completedPercent * 100).toInt();

                if (percentToShow >= 100) {
                  return Container(
                    height: 75,
                    // height: MediaQuery.of(context).size.height * 0.075,
                    padding: EdgeInsets.fromLTRB(
                        16 * fem, 0 * fem, 0 * fem, 0 * fem),
                    child: Row(
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const Text(
                            'Daily goals',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                              height: 0,
                            ),
                          ),
                          SizedBox(
                            width: 8 * fem,
                          ),
                          Image.asset(
                              'assets/images/dashboard/completed_tick.png'),
                        ]),
                  );
                } else {
                  return WebDailyGoalProgress(
                      completedPercent: completedPercent,
                      percentToShow: percentToShow);
                }
              } else {
                return const WebDailyGoalProgress(
                    completedPercent: 0, percentToShow: 0);
              }
            },
          ),
        ),
      ],
    );
  }
}
