import 'package:flutter/material.dart';
import 'package:skillapp/core/configs/configs.dart';

class TestCardWidget extends StatelessWidget {
  final String title;
  final String description;
  final String timerText;
  final List<String> subjectTags;
  final VoidCallback? onStartPressed;
  final String imageAsset;
  final double? height;
  final bool showStartButton;
  final String? completedMessage;
  final bool showComingSoonButton;

  const TestCardWidget(
      {super.key,
      required this.title,
      required this.description,
      required this.timerText,
      required this.subjectTags,
      this.onStartPressed,
      this.imageAsset = 'assets/images/dashboard/test_Assessment_img.png',
      this.height,
      this.showStartButton = true,
      this.completedMessage,
      this.showComingSoonButton = false});

  // Helper method to build subject tag
  Widget _buildSubjectTag(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: const [
          BoxShadow(
            color: Color(0x14000000),
            blurRadius: 28,
            offset: Offset(0, 0),
            spreadRadius: 0,
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top image section with timer
          Container(
            height: showComingSoonButton ? 120 : 150,
            decoration: const BoxDecoration(
              color: Color(0xFFF5F0D0), // Light yellow background
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              children: [
                Center(
                  child: Image.asset(
                    imageAsset,
                    height: showComingSoonButton ? 80 : 100,
                  ),
                ),
                // dont remove it might be required later for timer
                /* if (showStartButton)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.timer, size: 16),
                          const SizedBox(width: 4),
                          Text(timerText, style: const TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                  ),*/
              ],
            ),
          ),
          // Content section
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(height: 16),
                // Subject tags
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      subjectTags.map((tag) => _buildSubjectTag(tag)).toList(),
                ),
                const SizedBox(height: 16),
                // Conditional Start button or Completed message

                if (showComingSoonButton)
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                        side: const BorderSide(
                            width: 1,
                            //color: Color(0xFF0075FF),
                            color: kPrimaryColor),
                      ),
                    ),
                    child: const Text(
                      'Coming Soon',
                      style: TextStyle(
                        // color: Color(0xFF0052B3),
                        color: kPrimaryColor,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1,
                      ),
                    ),
                  )
                else if (showStartButton && onStartPressed != null)
                  ElevatedButton(
                    onPressed: onStartPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF50409A),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      minimumSize: const Size(100, 40),
                    ),
                    child: const Text('Start'),
                  )
                else if (!showStartButton)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          completedMessage ?? 'Completed for today',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
