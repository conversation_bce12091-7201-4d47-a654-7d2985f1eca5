import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/web_learning_info.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/web_daily_pratice_gridview.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/test_card_widget.dart';
import 'package:skillapp/src/home/<USER>/widgets/web/subject_card_widget.dart';
import 'package:skillapp/src/test/domain/common/tests_enum.dart';
import 'package:skillapp/src/test/presentation/blocs/tests_bloc.dart';

class WebDashboardWidget extends StatefulWidget {
  const WebDashboardWidget({
    super.key,
  });

  @override
  State<WebDashboardWidget> createState() => _WebDashboardWidgetState();
}

class _WebDashboardWidgetState extends State<WebDashboardWidget> {
  @override
  void initState() {
    super.initState();
    // Check if daily test is attempted when dashboard loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final testsBloc = context.read<TestsBloc>();
      if (!testsBloc.isClosed) {
        testsBloc.add(CheckIsDailyTestAttemptedEvent());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        width: double.infinity,
        //This parameter cannot be made global as the modal panels are not able to access the context of the parent widget
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left side - Learning Info
                Expanded(
                  flex: 1,
                  child: LearningInfo(),
                ),
                SizedBox(width: 40), // Spacing between widgets
                // Right side - Daily Practice Grid
                Expanded(
                  flex: 1,
                  child: WebDailyPraticeGridview(),
                ),
              ],
            ),

            const SizedBox(height: 40), // Spacing between rows

            // Second row - Test widgets as per screenshot
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Daily Test Card with BlocBuilder
                Expanded(
                  flex: 1,
                  child: BlocBuilder<TestsBloc, TestsStateInitial>(
                    builder: (context, state) {
                      // Determine if start button should be shown
                      bool showStartButton = true;
                      if (state is IsDailyTestAttemptedState) {
                        print(
                            "JB: Is daily test attempted? ${state.isAttempted}");
                        showStartButton = !state.isAttempted;
                      }

                      return TestCardWidget(
                        height: 370,
                        title: 'Daily test',
                        description:
                            'Practice daily with a new set of questions. Complete the test within 24 hours to keep improving and track your progress.',
                        timerText: '00:05:32',
                        subjectTags: const [
                          //  'Reading',
                          'Maths',
                          'Thinking skills'
                        ],
                        showStartButton: showStartButton,
                        completedMessage: 'Daily test completed for today',
                        onStartPressed: showStartButton
                            ? () {
                                // Dispatch event to fetch a new sectional test
                                final testsBloc = context.read<TestsBloc>();
                                if (!testsBloc.isClosed) {
                                  testsBloc.add(
                                    const FetchTodaysDailyTestEvent(),
                                  );

                                  // Navigate to daily test confirmation screen
                                  context.push('/daily-test-confirmation');
                                } else {
                                  // Handle the case where the bloc is closed
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                          'Unable to start test. Please try again.'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              }
                            : null,
                      );
                    },
                  ),
                ),

                const SizedBox(width: 40), // Spacing between cards

                // Full Mock Test Card
                
                Expanded(
                  flex: 1,
                  child: Expanded(
                    child: SubjectCardWidget(
                      height: 370,
                      title: 'Reading',
                      showComingSoon: true,
                      description:
                          'Boost your child\'s reading comprehension with passages that prepare them for the selective test.',
                      imageAsset:
                          'assets/images/dashboard/sectional/sectional_test_reading.png',
                      backgroundColor: const Color(0xFFE6EEFF),
                      onStartPressed: () {
                        // Handle reading start action
                      },
                    ),
                  ),

                  /*TestCardWidget(
                    height: 370,
                    title: 'Full mock test',
                    description:
                        'Take a new test every day to stay sharp and track your progress.',
                    timerText: '2:00 hrs',
                    showComingSoonButton: true,
                    subjectTags: const ['Reading', 'Maths', 'Thinking skills'],
                    onStartPressed: () {
                      // Navigate to test instructions screen with subject
                      // Commenting this for now and placing comming soon button
                      /* context.push('/test-instructions', extra: {
                        'subject': 'maths',
                      });*/
                    },
                  ),*/
                ),
              ],
            ),

            const SizedBox(height: 40), // Spacing between rows

            // Third row - Subject cards
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Reading Card
                // Spacing between cards

                // Maths Card
                Expanded(
                  child: SubjectCardWidget(
                    height: 300,
                    title: 'Maths',
                    description:
                        'Strengthen your child\'s problem-solving skills with targeted maths exercises.',
                    imageAsset:
                        'assets/images/dashboard/sectional/sectional_test_protractor_maths.png',
                    backgroundColor: const Color(0xFFFFECEC),
                    onStartPressed: () {
                      // Dispatch event to fetch a new sectional test
                      final testsBloc = context.read<TestsBloc>();
                      if (!testsBloc.isClosed) {
                        testsBloc.add(
                          const FetchNewSectionalTestEvent(subject: 'maths'),
                        );

                        // Navigate to test instructions screen with subject
                        context.push('/test-instructions', extra: {
                          'subject': 'maths',
                          'testType': TestTypes.sectional,
                        });
                      } else {
                        // Handle the case where the bloc is closed
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content:
                                Text('Unable to start test. Please try again.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                  ),
                ),

                const SizedBox(width: 20), // Spacing between cards

                // Thinking Skills Card
                Expanded(
                  child: SubjectCardWidget(
                    height: 300,
                    title: 'Thinking Skills',
                    description:
                        'Enhance your child\'s critical thinking with engaging reasoning challenges.',
                    imageAsset:
                        'assets/images/dashboard/sectional/sectional_test_thinkingskills.png',
                    backgroundColor: const Color(0xFFF5E6FF),
                    onStartPressed: () {
                      // Dispatch event to fetch a new sectional test
                      final testsBloc = context.read<TestsBloc>();
                      if (!testsBloc.isClosed) {
                        testsBloc.add(
                          const FetchNewSectionalTestEvent(subject: 'thinking'),
                        );

                        // Navigate to test instructions screen with subject
                        context.push('/test-instructions', extra: {
                          'subject': 'thinking',
                          'testType': TestTypes.sectional,
                        });
                      } else {
                        // Handle the case where the bloc is closed
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content:
                                Text('Unable to start test. Please try again.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                  ),
                ),

                const SizedBox(width: 20), // Spacing between cards

                Expanded(
                  child: SubjectCardWidget(
                    height: 300,
                    title: 'Reading',
                    showComingSoon: true,
                    description:
                        'Boost your child\'s reading comprehension with passages that prepare them for the selective test.',
                    imageAsset:
                        'assets/images/dashboard/sectional/sectional_test_reading.png',
                    backgroundColor: const Color(0xFFE6EEFF),
                    onStartPressed: () {
                      // Handle reading start action
                    },
                  ),
                ),

                const SizedBox(width: 20),

                // Writing Card
                Expanded(
                  child: SubjectCardWidget(
                    height: 300,
                    title: 'Writing',
                    showComingSoon: true,
                    description:
                        'Improve your child\'s writing skills with tasks that foster clear and effective communication.',
                    imageAsset:
                        'assets/images/dashboard/sectional/sectional_test_writing.png',
                    backgroundColor: const Color(0xFFE6FFEF),
                    onStartPressed: () {
                      // Handle writing start action
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(
              height: 40,
            ),
          ],
        ),
      ),
    );
  }
}
